import {type RouteObject} from 'react-router';
import MainLayout from '../Layouts/MainLayout/MainLayout';

/**
 * Routes that require authorization are protected using the ProtectedRouteWrapper component (default authorization is user authentication).
 * Authenticated users are redirected to the home page using the AuthRedirectWrapper component.
 *
 * @returns An array of RouteObject defining the routes of the application.
 */
export const getRouteConfig = (): RouteObject[] => {
  return [
    {
      path: '/*',
      element: <MainLayout />,
    },
  ];
};
