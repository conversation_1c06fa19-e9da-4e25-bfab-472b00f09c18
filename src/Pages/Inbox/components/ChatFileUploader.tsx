import React from 'react';
import {FilePlus2} from 'lucide-react';
import {useDropzone} from 'react-dropzone';

type Props = {
  onFileSelect: (file: File) => void;
};

const ChatFileUploader: React.FC<Props> = ({onFileSelect}) => {
  const {getRootProps, getInputProps} = useDropzone({
    accept: {
      'image/*': [],
      'application/pdf': [],
      'video/*': [],
      'audio/*': [],
      'text/*': [],
    },
    onDrop: acceptedFiles => {
      if (acceptedFiles.length > 0) {
        onFileSelect(acceptedFiles[0]);
      }
    },
  });

  return (
    <div {...getRootProps()} className="flex items-center gap-2" data-testid="file-uploader">
      <input {...getInputProps()} />
      <FilePlus2 data-testid="file-upload-icon" size={20} className="text-[var(--text-gray-color)]" />
    </div>
  );
};

export default ChatFileUploader;
