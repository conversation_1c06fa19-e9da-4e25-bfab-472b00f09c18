import {render, screen, waitFor} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';

import {MockApiTabContentDb} from '../mockData/mock-chat-content';
import {InboxTabsEnum} from '@/enums/inbox-tabs.enum';
import {InboxTabContent} from '../components/InboxTabContent';

// Mock child components
vi.mock('../components/InboxSidebar', () => ({
  InboxSidebar: vi.fn(({sideBarContent}) => <div data-testid="inbox-sidebar">{JSON.stringify(sideBarContent)}</div>),
}));

vi.mock('../components/InboxChatContent', () => ({
  InboxChatContent: vi.fn(() => <div data-testid="inbox-chat-content" />),
}));

// Mock the TabsContent component
vi.mock('@/components/ui/tabs', () => ({
  TabsContent: ({children, value, className}: {children: React.ReactNode; value: string; className: string}) => (
    <div data-testid={`mock-tabs-content-${value}`} className={className}>
      {children}
    </div>
  ),
}));

describe('InboxTabContent', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockTabDataActive = {
    tabId: 1,
    tabName: InboxTabsEnum.ACTIVE,
    chatCount: 5,
  };

  const mockTabDataQueued = {
    tabId: 2,
    tabName: InboxTabsEnum.QUEUED,
    chatCount: 3,
  };

  const mockTabDataNonExistent = {
    tabId: 99,
    tabName: InboxTabsEnum.MISSED, // Using a valid enum, but ID won't match
    chatCount: 0,
  };

  test('renders TabsContent with correct value and children', () => {
    render(<InboxTabContent tabData={mockTabDataActive} />);
    const tabsContent = screen.getByTestId(`mock-tabs-content-${mockTabDataActive.tabName}`);
    expect(tabsContent).toBeInTheDocument();
    expect(screen.getByTestId('inbox-sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('inbox-chat-content')).toBeInTheDocument();
  });

  test('InboxSidebar receives correct selectedTabContent for matching tabId', async () => {
    const {InboxSidebar} = await import('../components/InboxSidebar');
    render(<InboxTabContent tabData={mockTabDataActive} />);

    await waitFor(() => {
      expect(InboxSidebar).toHaveBeenCalledWith(
        expect.objectContaining({
          sideBarContent: MockApiTabContentDb.find(res => res.tabId === mockTabDataActive.tabId),
        }),
        undefined,
      );
    });
  });

  test('InboxSidebar receives correct selectedTabContent for another matching tabId', async () => {
    const {InboxSidebar} = await import('../components/InboxSidebar');
    render(<InboxTabContent tabData={mockTabDataQueued} />);

    await waitFor(() => {
      expect(InboxSidebar).toHaveBeenCalledWith(
        expect.objectContaining({
          sideBarContent: MockApiTabContentDb.find(res => res.tabId === mockTabDataQueued.tabId),
        }),
        undefined,
      );
    });
  });

  test('selectedTabContent updates when tabData.tabId changes', async () => {
    const {InboxSidebar} = await import('../components/InboxSidebar');
    const {rerender} = render(<InboxTabContent tabData={mockTabDataActive} />);

    await waitFor(() => {
      expect(InboxSidebar).toHaveBeenCalledWith(
        expect.objectContaining({
          sideBarContent: MockApiTabContentDb.find(res => res.tabId === mockTabDataActive.tabId),
        }),
        undefined,
      );
    });

    // Rerender with new tabData
    rerender(<InboxTabContent tabData={mockTabDataQueued} />);

    await waitFor(() => {
      expect(InboxSidebar).toHaveBeenCalledWith(
        expect.objectContaining({
          sideBarContent: MockApiTabContentDb.find(res => res.tabId === mockTabDataQueued.tabId),
        }),
        undefined,
      );
    });
  });

  test('renders InboxNoChatView when chatList is empty', () => {
    const emptyTabData = {
      tabId: 3,
      tabName: InboxTabsEnum.ARCHIVED,
      chatCount: 0,
    };
    render(<InboxTabContent tabData={emptyTabData} />);
    expect(screen.getByTestId('inbox-no-chat-view')).toBeInTheDocument();
  });

  test('does not render InboxNoChatView when chatList is not empty', () => {
    render(<InboxTabContent tabData={mockTabDataActive} />);
    const noChatView = screen.queryByTestId('inbox-no-chat-view');
    expect(noChatView).toBeNull();
  });

  test('renders TabsContent with the correct className', () => {
    const className = 'flex-1 flex flex-col min-h-0';
    render(<InboxTabContent tabData={mockTabDataActive} />);
    const tabsContent = screen.getByTestId(`mock-tabs-content-${mockTabDataActive.tabName}`);
    expect(tabsContent).toHaveClass(className);
  });

  test('InboxChatContent receives correct chatContent prop', () => {
    render(<InboxTabContent tabData={mockTabDataActive} />);
    expect(screen.getByTestId('inbox-chat-content')).toBeInTheDocument();
  });

  test('InboxSidebar receives undefined sideBarContent when tabId does not match', async () => {
    const {InboxSidebar} = await import('../components/InboxSidebar');
    render(<InboxTabContent tabData={mockTabDataNonExistent} />);

    await waitFor(() => {
      expect(InboxSidebar).not.toHaveBeenCalledWith(
        expect.objectContaining({
          sideBarContent: undefined,
        }),
        undefined,
      );
    });
  });
});
