'use client';

import type {ColumnDef} from '@tanstack/react-table';
import AvailabilityStatus from '@/components/ui/availabilityStatus';
import NameWithAvatar from '@/components/name-avtar';
import {type AgentsDetails} from '@/types';
import {formatDate} from '@/utils';
import type {TFunction} from 'i18next';
import ActionsCell from '@/components/action-cell';
import StatusCell from '@/components/status-cell';

export const agentColumns = (t: TFunction<'translation', undefined>): ColumnDef<AgentsDetails>[] => [
  {
    accessorKey: 'name',
    header: t('NAME'),
    id: 'name',
    cell: ({row}) => (
      <div className="text-left">
        <NameWithAvatar name={row.getValue('name')} />
      </div>
    ),
  },
  {
    accessorKey: 'email',
    id: 'email',
    header: t('EMAIL'),
    cell: ({row}) => <div className="text-left">{row.getValue('email')}</div>,
  },
  {
    accessorKey: 'mobile',
    id: 'mobile',
    header: t('MOBILE'),
    cell: ({row}) => <div className="text-left">{row.getValue('mobile')}</div>,
  },
  {
    accessorKey: 'departmentName',
    id: 'department',
    header: t('DEPARTMENT'),
    cell: ({row}) => <div className="text-left">{row.getValue('department')}</div>,
  },
  {
    accessorKey: 'createdAt',
    id: 'createdAt',
    header: t('CREATED_ON'),
    cell: ({row}) => <div className="text-left">{formatDate(row.getValue('createdAt'))}</div>,
  },
  {
    accessorKey: 'availability',
    header: t('AVAILABILITY'),
    cell: ({row}) => (
      <div className="flex justify-start">
        <AvailabilityStatus status={row.getValue('availability')} />
      </div>
    ),
  },
  {
    accessorKey: 'status',
    header: t('STATUS'),
    cell: ({row}) => <StatusCell agent={row.original} />,
  },
  {
    id: 'actions',
    header: t('ACTIONS'),
    cell: ({row}) => <ActionsCell row={row} />,
  },
];
