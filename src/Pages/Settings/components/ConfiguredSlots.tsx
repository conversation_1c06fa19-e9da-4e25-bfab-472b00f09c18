import {Card} from '@/components/ui/card';
import {Pencil} from 'lucide-react';
import {useNavigate} from 'react-router-dom';
import {RouteConstant} from '@/constants';

export const ConfiguredSlots = () => {
  const navigate = useNavigate();
  return (
    <>
      <Card className="p-4 pr-6 relative w-245 h-27.5 border-[var(--color-border-input)]">
        <div className="absolute top-5 left-5 flex flex-col text-sm leading-5 space-y-[5px]">
          <h4 className="font-medium text-[var(--text-label)]">Default Business Hours</h4>
          <p className="font-normal text-[var(--color-muted-text)]">Same everyday: 10:00AM to 06:00PM</p>
          <p className="font-normal text-[var(--color-muted-text)]">All Departments</p>
        </div>
        <Pencil
          data-testid="pencil-icon"
          className="h-3.5 w-3.5 text-[var(--color-tab-text-default)] cursor-pointer absolute top-5 right-5"
          onClick={() => void navigate(RouteConstant.EDIT_BUSINESS_HOURS)}
        />
      </Card>
    </>
  );
};
