import {render, screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';

import type {InboxChatListContentProps} from '../types/inbox-component-props';
import {InboxTabsEnum} from '@/enums/inbox-tabs.enum';
import {InboxChatList} from '../components/InboxChatList';

// Mock Avatar and Badge components
vi.mock('@/components/ui/avatar', () => ({
  Avatar: ({children}: {children: React.ReactNode}) => <div data-testid="mock-avatar">{children}</div>,
  AvatarFallback: ({children}: {children: React.ReactNode}) => (
    <span data-testid="mock-avatar-fallback">{children}</span>
  ),
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({children, className}: {children: React.ReactNode; className: string}) => (
    <span data-testid="mock-badge" className={className}>
      {children}
    </span>
  ),
}));

const mockChatListContent: InboxChatListContentProps = {
  chatListContent: {
    tabId: 1,
    tabName: InboxTabsEnum.ACTIVE,
    chatList: [
      {
        channelId: 1,
        recepientId: 101,
        recepientName: 'Alice Smith',
        recepientProfilrUrl: 'url1',
        latestMessageInfo: {
          message: 'Hello there!',
          timestamp: '10:00 AM',
          unreadMessageCount: 2,
          requestLabel: '',
        },
      },
      {
        channelId: 2,
        recepientId: 102,
        recepientName: 'Bob Johnson',
        recepientProfilrUrl: 'url2',
        latestMessageInfo: {
          message: 'How are you?',
          timestamp: '09:30 AM',
          unreadMessageCount: 0,
          requestLabel: '',
        },
      },
      {
        channelId: 3,
        recepientId: 103,
        recepientName: 'Charlie Brown',
        recepientProfilrUrl: 'url3',
        latestMessageInfo: {
          message: 'Chat request received.',
          timestamp: '09:00 AM',
          unreadMessageCount: 1,
          requestLabel: 'Chat Request',
        },
      },
    ],
  },
};

describe('InboxChatList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders the correct number of chat items', () => {
    const {container} = render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    const chatItems = container.querySelectorAll('.chat-item');
    expect(chatItems).toHaveLength(mockChatListContent.chatListContent.chatList.length);
  });

  test('displays recepient name and timestamp', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    expect(screen.getByText('Alice Smith')).toBeInTheDocument();
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
    expect(screen.getByText('09:30 AM')).toBeInTheDocument();
  });

  test('displays message content', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    expect(screen.getByText('Hello there!')).toBeInTheDocument();
    expect(screen.getByText('How are you?')).toBeInTheDocument();
  });

  test('renders AvatarFallback with correct initials', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    const initials = screen.getAllByTestId('mock-avatar-fallback');
    expect(initials[0]).toHaveTextContent('AS');
    expect(initials[1]).toHaveTextContent('BJ');
    expect(initials[2]).toHaveTextContent('CB');
  });

  test('renders Badge with unread message count when count > 0 and no requestLabel', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    const aliceMessage = screen.getByText('Hello there!');
    const aliceParent = aliceMessage.closest('.chat-item');
    expect(aliceParent?.querySelector('[data-testid="mock-badge"]')).toHaveTextContent(
      mockChatListContent.chatListContent.chatList[0].latestMessageInfo.unreadMessageCount.toString(),
    );
  });

  test('renders Badge with requestLabel when requestLabel is present', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    const charlieMessage = screen.getByText('Chat request received.');
    const charlieParent = charlieMessage.closest('.chat-item');
    expect(charlieParent?.querySelector('[data-testid="mock-badge"]')).toHaveTextContent('Chat Request');
  });

  test('does not render Badge when unread message count is 0', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    const bobMessage = screen.getByText('How are you?');
    const bobParent = bobMessage.closest('.chat-item');
    expect(bobParent?.querySelector('[data-testid="mock-badge"]')).not.toBeInTheDocument();
  });

  test('applies selected class on click', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);
    const aliceItem = screen.getByText('Alice Smith').closest('.chat-item');
    const bobItem = screen.getByText('Bob Johnson').closest('.chat-item');

    expect(aliceItem).not.toHaveClass('selected');
    expect(bobItem).not.toHaveClass('selected');

    fireEvent.click(aliceItem!);
    expect(aliceItem).toHaveClass('selected');
    expect(bobItem).not.toHaveClass('selected');

    fireEvent.click(bobItem!);
    expect(aliceItem).not.toHaveClass('selected');
    expect(bobItem).toHaveClass('selected');
  });

  test('handles empty chatList gracefully', () => {
    const emptyChatListContent: InboxChatListContentProps = {
      chatListContent: {
        tabId: 0,
        tabName: InboxTabsEnum.ACTIVE,
        chatList: [],
      },
    };
    render(<InboxChatList chatListContent={emptyChatListContent.chatListContent} />);
    expect(screen.queryAllByRole('button')).toHaveLength(0);
    expect(screen.queryByText('Alice Smith')).not.toBeInTheDocument();
  });

  test('message text styling based on unreadMessageCount or requestLabel', () => {
    render(<InboxChatList chatListContent={mockChatListContent.chatListContent} />);

    // Alice: unreadMessageCount > 0
    const aliceMessage = screen.getByText('Hello there!');
    expect(aliceMessage).toHaveClass('font-medium');
    expect(aliceMessage).not.toHaveClass('font-normal text-[#929292]');

    // Bob: unreadMessageCount === 0, no requestLabel
    const bobMessage = screen.getByText('How are you?');
    expect(bobMessage).toHaveClass('font-normal text-[var(--color-muted-text)]');
    expect(bobMessage).not.toHaveClass('font-medium');

    // Charlie: requestLabel present
    const charlieMessage = screen.getByText('Chat request received.');
    expect(charlieMessage).toHaveClass('font-medium');
    expect(charlieMessage).not.toHaveClass('font-normal text-[#929292]');
  });
});
