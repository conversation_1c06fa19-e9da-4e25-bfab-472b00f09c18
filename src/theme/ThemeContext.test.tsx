import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, act} from '@testing-library/react';
import {ThemeContext} from './ThemeContext';
import {ThemeManager} from './ThemeManager';
import {data} from './data';
import React from 'react';
import {ThemeProvider} from './ThemeProvider';
import type {ThemeVariables} from '@/types';

// Mock ThemeManager
vi.mock('./ThemeManager', () => ({
  ThemeManager: {
    apply: vi.fn(),
    persist: vi.fn(),
    load: vi.fn(),
  },
}));

// Create a test component that consumes the context
const TestComponent = () => {
  const context = React.useContext(ThemeContext);
  return (
    <div>
      <div data-testid="theme-state">{JSON.stringify(context?.themeState)}</div>
      <button
        data-testid="set-theme-btn"
        onClick={() =>
          context?.setTheme({
            primary: 'test-color',
            foreground: 'test-fg-color',
            background: 'test-bg-color',
          })
        }
      >
        Set Theme
      </button>
    </div>
  );
};

describe('ThemeProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render children', () => {
    render(
      <ThemeProvider>
        <div data-testid="child">Child Component</div>
      </ThemeProvider>,
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('should apply default theme on mount', () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    expect(ThemeManager.apply).toHaveBeenCalledWith(data);
    expect(ThemeManager.persist).toHaveBeenCalledWith(data);
  });

  it('should provide setTheme function that updates theme', () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>,
    );

    const newTheme: ThemeVariables = {
      primary: 'test-color',
      foreground: 'test-fg-color',
      background: 'test-bg-color',
    };

    act(() => {
      screen.getByTestId('set-theme-btn').click();
    });

    expect(ThemeManager.apply).toHaveBeenCalledWith(newTheme);
    expect(ThemeManager.persist).toHaveBeenCalledWith(newTheme);
    expect(screen.getByTestId('theme-state').textContent).toContain('test-color');
  });
});
