import {render, screen} from '@testing-library/react';
import {describe, it, expect} from 'vitest';
import {TopBar} from './TopBar';
import {vi} from 'vitest';

vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: () => ({
      t: (key: string) => {
        if (key === 'LANGUAGE_EN') {
          return 'en-US';
        }
        return key;
      },
      i18n: {
        dir: () => 'ltr',
        language: 'en-US',
      },
    }),
    initReactI18next: actual.initReactI18next, // Ensure initReactI18next is explicitly returned
  };
});

describe('TopBar Component', () => {
  it('renders without crashing', () => {
    render(<TopBar />);
    expect(screen.getByRole('banner')).toBeInTheDocument();
  });

  it('renders the language selection button', () => {
    render(<TopBar />);
    const languageButton = screen.getByLabelText('ARIA_LABEL_SELECT_LANGUAGE');
    expect(languageButton).toBeInTheDocument();
    expect(languageButton).toHaveTextContent('en-US');
  });

  it('renders the notifications button', () => {
    render(<TopBar />);
    const notificationsButton = screen.getByLabelText('ARIA_LABEL_NOTIFICATIONS');
    expect(notificationsButton).toBeInTheDocument();
  });
});
