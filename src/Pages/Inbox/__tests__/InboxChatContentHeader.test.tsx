import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxChatContentHeader} from '../components/InboxChatContentHeader';
import {MESSAGES} from '@/constants/messages.constant';

// Mock necessary modules
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({
    children,
    onClick,
    variant,
    className,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    variant?: string;
    className?: string;
  }) => (
    <button data-testid="mock-button" onClick={onClick} className={`${variant} ${className}`}>
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/avatar', () => ({
  Avatar: ({children, className}: {children: React.ReactNode; className: string}) => (
    <div data-testid="mock-avatar" className={className}>
      {children}
    </div>
  ),
  AvatarFallback: ({children, className}: {children: React.ReactNode; className: string}) => (
    <span data-testid="mock-avatar-fallback" className={className}>
      {children}
    </span>
  ),
}));

vi.mock('lucide-react', async () => {
  const actual = await vi.importActual('lucide-react');
  return {
    ...actual,
    Video: () => <svg data-testid="mock-video-icon" />,
    PhoneCall: () => <svg data-testid="mock-phone-call-icon" />,
    ScreenShare: () => <svg data-testid="mock-screen-share-icon" />,
    EllipsisVertical: ({onClick, className}: {onClick?: () => void; className?: string}) => (
      <svg data-testid="mock-ellipsis-vertical-icon" onClick={onClick} className={className} />
    ),
  };
});

vi.mock('../components/InboxTransferChatDialog', () => ({
  InboxTransferChatDialog: vi.fn(({isOpen, onClose}) => {
    if (!isOpen) return null;
    return (
      <div data-testid="mock-inbox-transfer-chat-dialog" data-is-open={isOpen}>
        <button onClick={onClose} data-testid="transfer-dialog-close-button">
          Close Transfer Dialog
        </button>
      </div>
    );
  }),
}));

describe('InboxChatContentHeader', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    vi.resetModules(); // Reset modules to ensure fresh mocks
    // Assign to global variables or use directly in tests
    // For simplicity, we'll re-import within tests if needed, or rely on the mock being applied globally.
  });

  test('renders user information and action icons', () => {
    render(<InboxChatContentHeader />);
    expect(screen.getByText('Dave Grohl')).toBeInTheDocument();
    expect(screen.getByText('Amazon Ecom')).toBeInTheDocument();
    expect(screen.getByTestId('mock-avatar')).toBeInTheDocument();
    expect(screen.getByTestId('mock-avatar-fallback')).toHaveTextContent('DG');
    expect(screen.getByTestId('mock-phone-call-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-video-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-screen-share-icon')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: MESSAGES.INBOX_END_CHAT_BUTTON})).toBeInTheDocument();
    expect(screen.getByTestId('mock-ellipsis-vertical-icon')).toBeInTheDocument();
  });

  test('clicking EllipsisVertical icon toggles transfer chat option visibility', async () => {
    render(<InboxChatContentHeader />);
    const ellipsisIcon = screen.getByTestId('mock-ellipsis-vertical-icon');

    // Initially, transfer chat option should not be visible
    expect(screen.queryByText(MESSAGES.TRANSFER_CHAT)).not.toBeInTheDocument();

    // Click to show
    fireEvent.click(ellipsisIcon);
    await waitFor(() => {
      expect(screen.getByText(MESSAGES.TRANSFER_CHAT)).toBeInTheDocument();
    });

    // Click again to hide
    fireEvent.click(ellipsisIcon);
    await waitFor(() => {
      expect(screen.queryByText(MESSAGES.TRANSFER_CHAT)).not.toBeInTheDocument();
    });
  });

  test('clicking transfer chat option opens InboxTransferChatDialog and closes transfer chat option', async () => {
    const {InboxTransferChatDialog} = await import('../components/InboxTransferChatDialog');
    render(<InboxChatContentHeader />);
    const ellipsisIcon = screen.getByTestId('mock-ellipsis-vertical-icon');

    fireEvent.click(ellipsisIcon); // Show transfer chat option
    await waitFor(() => {
      const transferChatOption = screen.getByText(MESSAGES.TRANSFER_CHAT);
      fireEvent.click(transferChatOption);
    });

    await waitFor(() => {
      expect(InboxTransferChatDialog).toHaveBeenCalledWith(
        expect.objectContaining({
          isOpen: true,
        }),
        undefined,
      );
      expect(screen.getByTestId('mock-inbox-transfer-chat-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('mock-inbox-transfer-chat-dialog')).toHaveAttribute('data-is-open', 'true');
      expect(screen.queryByText(MESSAGES.TRANSFER_CHAT)).not.toBeInTheDocument(); // Transfer chat option should be hidden
    });
  });

  test('InboxTransferChatDialog closes when its onClose is called', async () => {
    render(<InboxChatContentHeader />);
    const ellipsisIcon = screen.getByTestId('mock-ellipsis-vertical-icon');

    fireEvent.click(ellipsisIcon); // Show transfer chat option
    await waitFor(() => {
      const transferChatOption = screen.getByText(MESSAGES.TRANSFER_CHAT);
      fireEvent.click(transferChatOption);
    });

    await waitFor(() => {
      expect(screen.queryByTestId('mock-inbox-transfer-chat-dialog')).toBeInTheDocument(); // Ensure it's present before closing
    });

    fireEvent.click(screen.getByTestId('transfer-dialog-close-button'));

    await waitFor(() => {
      expect(screen.queryByTestId('mock-inbox-transfer-chat-dialog')).not.toBeInTheDocument();
    });
  });
});
