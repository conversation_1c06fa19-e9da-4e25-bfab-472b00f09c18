import React from 'react';
import {useTranslation} from 'react-i18next';

interface ChatFilePreviewProps {
  file: File;
  fileType?: string;
  fileName?: string;
}

const ChatFilePreview: React.FC<ChatFilePreviewProps> = ({file, fileType, fileName}) => {
  const {t} = useTranslation();
  const url = URL.createObjectURL(file);
  const baseType = fileType?.split('/')[0]; // "image", "video", "audio", etc.

  switch (baseType) {
    case 'image':
      return (
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          title={t('OPEN_IMAGE_IN_NEW_TAB')}
          data-testid="file-preview"
        >
          <img src={url} alt={fileName} className="max-h-32 max-w-full mt-2 rounded" />
        </a>
      );

    case 'video':
      return (
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          title={t('OPEN_VIDEO_IN_NEW_TAB')}
          data-testid="file-preview"
        >
          <video controls className="max-h-32 max-w-full mt-2 rounded cursor-pointer">
            <source src={url} type={fileType} />
            {/* Empty captions track for accessibility */}
            <track kind="captions" />
            {t('BROWSER_VIDEO_NOT_SUPPORT')}
          </video>
        </a>
      );

    case 'audio':
      return (
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          title={t('OPEN_AUDIO_IN_NEW_TAB')}
          data-testid="file-preview"
        >
          <audio controls className="mt-2 cursor-pointer">
            <source src={url} type={fileType} />
            {/* Empty captions track for accessibility */}
            <track kind="captions" />
            {t('BROWSER_AUDIO_NOT_SUPPORT')}
          </audio>
        </a>
      );

    default:
      return (
        <a
          href={url}
          download={fileName}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 underline mt-2 block"
          data-testid="file-preview"
        >
          {fileName}
        </a>
      );
  }
};

export default ChatFilePreview;
