import type {AgentsDetails} from '@/types';
import {AgentAvailability, AgentStatus} from '@/types';

export const dummyAgents: AgentsDetails[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000001',
    departmentId: 'Customer Experience',
    createdAt: '2024-06-01T08:00:00Z',
    availability: AgentAvailability.ONLINE,
    status: AgentStatus.ACTIVE,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000002',
    departmentId: 'Customer Experience',
    createdAt: '2024-06-02T08:00:00Z',
    availability: AgentAvailability.BUSY,
    status: AgentStatus.ACTIVE,
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000003',
    departmentId: 'Customer Experience',
    createdAt: '2024-06-10T08:00:00Z',
    availability: AgentAvailability.AWAY,
    status: AgentStatus.INACTIVE,
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000004',
    departmentId: 'Customer Experience',
    createdAt: '2024-06-10T08:00:00Z',
    availability: AgentAvailability.OFFLINE,
    status: AgentStatus.INACTIVE,
  },
  {
    id: '5',
    name: 'Emma Mark',
    email: '<EMAIL>',
    mobile: '+919999000005',
    departmentId: 'Customer Experience',
    createdAt: '2024-06-12T08:00:00Z',
    availability: AgentAvailability.ONLINE,
    status: AgentStatus.ACTIVE,
  },
  {
    id: '6',
    name: 'Liam Bose',
    email: '<EMAIL>',
    mobile: '+919999000006',
    departmentId: 'Customer Experience',
    createdAt: '2024-06-12T08:00:00Z',
    availability: AgentAvailability.OFFLINE,
    status: AgentStatus.INACTIVE,
  },
  {
    id: '7',
    name: 'Olivia Martinez',
    email: '<EMAIL>',
    mobile: '+919999000007',
    departmentId: 'Technical Assistance',
    createdAt: '2025-06-07T08:00:00Z',
    availability: AgentAvailability.ONLINE,
    status: AgentStatus.ACTIVE,
  },
  {
    id: '8',
    name: 'Noah Thompson',
    email: '<EMAIL>',
    mobile: '+919999000008',
    departmentId: 'Technical Assistance',
    createdAt: '2025-06-08T08:00:00Z',
    availability: AgentAvailability.OFFLINE,
    status: AgentStatus.INACTIVE,
  },
];
