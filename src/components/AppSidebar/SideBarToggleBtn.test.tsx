/* eslint-disable @typescript-eslint/no-explicit-any */
import {render, screen, fireEvent} from '@testing-library/react';
import {vi, describe, expect, it} from 'vitest';
import {SideBarToggleBtn} from './SideBarToggleBtn';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      dir: () => 'ltr', // Mock the dir function
    },
  }),
}));

// Mock the useSidebar hook
vi.mock('../ui', () => ({
  useSidebar: vi.fn(),
  Button: ({children, ...props}: any) => <button {...props}>{children}</button>,
}));

// import mocked useSidebar after mocking it
import {useSidebar} from '../ui';

describe('SideBarToggleBtn', () => {
  it('calls toggleSidebar on click', () => {
    const toggleSidebar = vi.fn();
    (useSidebar as any).mockReturnValue({
      toggleSidebar,
      state: 'collapsed',
    });

    render(<SideBarToggleBtn />);
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(toggleSidebar).toHaveBeenCalledTimes(1);
  });
});
