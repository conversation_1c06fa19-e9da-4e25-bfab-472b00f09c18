import {render, screen, act} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxSidebar} from '../components/InboxSidebar';

import type {IInboxChatContent} from '../types/inbox-tab-content';
import {InboxChatList} from '../components/InboxChatList'; // Import the mocked component directly
import {InboxTabsEnum} from '@/enums/inbox-tabs.enum';

// Mock child components
vi.mock('../components/InboxSidebarHeader', () => ({
  InboxSidebarHeader: vi.fn(() => <div data-testid="mock-inbox-sidebar-header" />),
}));

vi.mock('../components/InboxSearch', () => ({
  InboxSearch: vi.fn(({onSearchResults, activeTab}) => (
    <div data-testid="mock-inbox-search" data-active-tab={activeTab} onClick={() => onSearchResults([])} />
  )),
}));

vi.mock('../components/InboxChatList', () => ({
  InboxChatList: vi.fn(({chatListContent}) => (
    <div data-testid="mock-inbox-chat-list" data-chat-list-content={JSON.stringify(chatListContent)} />
  )),
}));

// Mock the Tabs component
vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({children, defaultValue, className}: {children: React.ReactNode; defaultValue: string; className: string}) => (
    <div data-testid="mock-tabs" data-default-value={defaultValue} className={className}>
      {children}
    </div>
  ),
  TabsList: ({children, className}: {children: React.ReactNode; className: string}) => (
    <div data-testid="mock-tabs-list" className={className}>
      {children}
    </div>
  ),
  TabsTrigger: ({value, children, className}: {value: string; children: React.ReactNode; className: string}) => (
    <button data-testid={`mock-tabs-trigger-${value}`} className={className}>
      {children}
    </button>
  ),
}));

const mockSideBarContent: IInboxChatContent = {
  tabId: 1,
  tabName: InboxTabsEnum.ACTIVE,
  chatList: [
    {
      channelId: 1,
      recepientId: 1,
      recepientName: 'Test User 1',
      recepientProfilrUrl: '',
      latestMessageInfo: {
        message: 'Hello',
        timestamp: '10:00 AM',
        unreadMessageCount: 0,
        requestLabel: '',
      },
    },
  ],
};

describe('InboxSidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders Tabs component with default value and correct classes', () => {
    render(<InboxSidebar sideBarContent={mockSideBarContent} />);
    const tabsComponent = screen.getByTestId('mock-tabs');
    expect(tabsComponent).toBeInTheDocument();
    expect(tabsComponent).toHaveAttribute('data-default-value', 'self');
    expect(tabsComponent).toHaveClass('flex', 'flex-col', 'flex-1', 'min-h-0', 'gap-0');
  });

  test('renders main container div with correct classes', () => {
    render(<InboxSidebar sideBarContent={mockSideBarContent} />);
    const mainContainer = screen.getByTestId('mock-tabs').parentElement; // Get the parent div of mock-tabs
    expect(mainContainer).toBeInTheDocument();
    expect(mainContainer).toHaveClass(
      'w-65',
      'min-w-60',
      'flex-shrink-0',
      'border-r',
      'border-border',
      'flex',
      'flex-col',
      'h-full',
    );
  });

  test('renders InboxSidebarHeader, InboxSearch, and InboxChatList components', () => {
    render(<InboxSidebar sideBarContent={mockSideBarContent} />);
    expect(screen.getByTestId('mock-inbox-sidebar-header')).toBeInTheDocument();
    expect(screen.getByTestId('mock-inbox-search')).toBeInTheDocument();
    expect(screen.getByTestId('mock-inbox-chat-list')).toBeInTheDocument();
  });

  test('InboxChatList receives displayedChatList prop initially', () => {
    render(<InboxSidebar sideBarContent={mockSideBarContent} />);

    expect(InboxChatList).toHaveBeenCalledWith(
      {
        chatListContent: {...mockSideBarContent, chatList: mockSideBarContent.chatList},
      },
      undefined,
    );
  });

  test('InboxSearch receives activeTab prop', () => {
    render(<InboxSidebar sideBarContent={mockSideBarContent} />);
    expect(screen.getByTestId('mock-inbox-search')).toHaveAttribute('data-active-tab', mockSideBarContent.tabName);
  });

  test('updates displayedChatList when onSearchResults is called', async () => {
    const {rerender} = render(<InboxSidebar sideBarContent={mockSideBarContent} />);
    const inboxSearch = screen.getByTestId('mock-inbox-search');

    await act(async () => {
      (inboxSearch as HTMLDivElement).click(); // Simulate calling onSearchResults with empty array
    });

    rerender(<InboxSidebar sideBarContent={mockSideBarContent} />); // Rerender to apply state updates

    expect(InboxChatList).toHaveBeenCalledWith(
      {
        chatListContent: {...mockSideBarContent, chatList: []},
      },
      undefined,
    );
  });

  test('updates displayedChatList when sideBarContent.chatList changes', async () => {
    const {rerender} = render(<InboxSidebar sideBarContent={mockSideBarContent} />);

    const newChatList = [
      {
        channelId: 2,
        recepientId: 2,
        recepientName: 'New User',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Hi',
          timestamp: '11:00 AM',
          unreadMessageCount: 0,
          requestLabel: '',
        },
      },
    ];
    const newSideBarContent = {...mockSideBarContent, chatList: newChatList};

    await act(async () => {
      rerender(<InboxSidebar sideBarContent={newSideBarContent} />);
    });

    expect(InboxChatList).toHaveBeenCalledWith(
      {
        chatListContent: {...newSideBarContent, chatList: newChatList},
      },
      undefined,
    );
  });
});
