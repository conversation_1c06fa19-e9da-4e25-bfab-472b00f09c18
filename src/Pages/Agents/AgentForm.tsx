'use client';

import {useF<PERSON>, Controller} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {agentSchema, type AgentFormValues} from '@/validations/agentSchema';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {useNavigate} from 'react-router-dom';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/high-res.css';
import {useCreateDepartmentMutation, useGetDepartmentsQuery} from '@/redux/departments/departmentSlice';
import {cn} from '@/lib/utils';
import {useTranslation} from 'react-i18next';
import {useEffect, useState} from 'react';
import {Separator} from '@/components/ui/separator';

interface AgentFormProps {
  onSubmit: (values: AgentFormValues) => void;
  isSubmitting?: boolean;
  isEditMode?: boolean;
  defaultValues?: AgentFormValues;
}

export function AgentForm({onSubmit, isSubmitting = false, isEditMode, defaultValues}: AgentFormProps) {
  const {t} = useTranslation();
  const {data: departments, isLoading: isDeptLoading} = useGetDepartmentsQuery();
  const deptData = departments?.data ?? [];
  const [createDepartment] = useCreateDepartmentMutation();

  const [showAddInput, setShowAddInput] = useState(false);
  const [newDeptName, setNewDeptName] = useState('');

  const [focusStates, setFocusStates] = useState({
    name: false,
    email: false,
    mobile: false,
    departmentId: false,
  });

  const {
    register,
    control,
    handleSubmit,
    formState: {errors, isValid, dirtyFields},
    reset,
    setValue,
    watch,
  } = useForm<AgentFormValues>({
    resolver: zodResolver(agentSchema),
    defaultValues: defaultValues ?? {
      name: '',
      email: '',
      mobile: '',
      departmentId: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, deptData, reset]);

  const formValues = watch();
  const navigate = useNavigate();

  const handleFormSubmit = (values: AgentFormValues) => {
    onSubmit(values);
  };

  const handleClose = () => {
    reset();
    void navigate('/teams');
  };

  const handleFocus = (field: keyof typeof focusStates) => {
    setFocusStates(prev => ({...prev, [field]: true}));
  };

  const handleBlur = (field: keyof typeof focusStates) => {
    setFocusStates(prev => ({...prev, [field]: false}));
  };

  const shouldFloat = (field: keyof typeof focusStates) => {
    return focusStates[field] || !!formValues[field];
  };

  const getLabelZIndex = (field: keyof typeof focusStates) => {
    return field === 'mobile' && focusStates.mobile ? 'z-0' : 'z-10';
  };

  const handleAddDepartment = async () => {
    if (!newDeptName.trim()) return;

    const newDept = {
      id: `custom-${Date.now()}`,
      name: newDeptName.trim(),
    };

    setValue('departmentId', newDept.id, {shouldValidate: true});
    setNewDeptName('');
    setShowAddInput(false);

    try {
      await createDepartment({name: newDept.name}).unwrap();
    } catch (err) {
      console.error('Failed to create department:', err);
      // optionally revert UI or show toast
    }
  };
  const getSubmitButtonLabel = () => {
    if (isSubmitting) {
      return isEditMode ? t('SAVING...') : t('ADDING...');
    }
    return isEditMode ? t('SAVE') : t('ADD');
  };

  const isSubmitDisabled = () => {
    if (isEditMode) {
      return !isValid || Object.keys(dirtyFields).length === 0 || isSubmitting;
    }
    return !isValid || isSubmitting;
  };

  return (
    <div className="p-6 bg-white shadow rounded-xl mt-4">
      <h2 className="text-md font-medium mb-4">{t('ENTER_AGENT_DETAILS')}</h2>
      <hr className="mb-6 border-gray-300" />
      <form onSubmit={e => void handleSubmit(handleFormSubmit)(e)} noValidate data-testid="agent-form">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-[600px]">
          {/* Name */}
          <div className="mb-1">
            <div className="relative">
              <div
                className={cn(
                  'absolute inset-0 pointer-events-none border rounded-md',
                  shouldFloat('name') ? 'border-primary' : 'border-input',
                  errors.name && 'border-red-500',
                )}
              >
                <span
                  className={cn(
                    'absolute px-1 bg-white text-xs transition-all duration-200 z-10',
                    shouldFloat('name')
                      ? '-top-2 left-3 text-xxs text-primary'
                      : 'top-1/2 left-3 -translate-y-1/2 text-[color:var(--color-placeholders)]',
                    errors.name && (shouldFloat('name') ? 'text-red-500' : 'text-[color:var(--color-placeholders)]'),
                  )}
                >
                  {t('AGENT_NAME')}
                </span>
              </div>
              <Input
                id="name"
                {...register('name')}
                className="h-11 placeholder:opacity-0 bg-transparent border-0 focus:ring-0 focus:border-0"
                onFocus={() => handleFocus('name')}
                onBlur={() => handleBlur('name')}
                data-testid="Name"
              />
            </div>
            {errors.name && <p className="text-xs text-red-500 mt-1 ml-1">{errors.name.message}</p>}
          </div>

          {/* Mobile */}
          <div className="mb-1">
            <div className="relative">
              <div
                className={cn(
                  'absolute inset-0 pointer-events-none border rounded-md',
                  shouldFloat('mobile') ? 'border-primary' : 'border-input',
                  errors.mobile && 'border-red-500',
                )}
              >
                <span
                  className={cn(
                    'absolute px-1 bg-white text-xs transition-all duration-200',
                    getLabelZIndex('mobile'),
                    shouldFloat('mobile')
                      ? '-top-2 left-3 text-xxs text-primary'
                      : 'top-1/2 left-19 -translate-y-1/2 text-[color:var(--color-placeholders)]',
                    errors.mobile &&
                      (shouldFloat('mobile') ? 'text-red-500' : 'text-[color:var(--color-placeholders)]'),
                  )}
                >
                  {t('MOBILE')}
                </span>
              </div>
              <PhoneInput
                country={'in'}
                value={formValues.mobile}
                onChange={value => {
                  const cleaned = value.replace(/[^\d]/g, '');
                  const withPlus = '+' + cleaned;
                  setValue('mobile', withPlus, {shouldValidate: true, shouldDirty: true});
                }}
                onFocus={() => handleFocus('mobile')}
                onBlur={() => handleBlur('mobile')}
                inputProps={{
                  name: 'mobile',
                  maxLength: 13, // extra safety: +91 + 10 digits
                  pattern: '[0-9]*',
                  inputMode: 'numeric',
                }}
                inputStyle={{
                  width: '100%',
                  height: '44px',
                  borderRadius: '0.375rem',
                  border: 'none',
                  backgroundColor: 'transparent',
                  paddingLeft: '3.1rem',
                  outline: 'none',
                  boxShadow: 'none',
                }}
                buttonStyle={{
                  border: 'none',
                  backgroundColor: 'transparent',
                  borderRadius: '0.375rem 0 0 0.375rem',
                }}
                containerStyle={{width: '100%'}}
                specialLabel=""
                placeholder=""
                autoFormat={false}
              />
            </div>
            {errors.mobile && <p className="text-xs text-red-500 mt-1 ml-1">{errors.mobile.message}</p>}
          </div>

          {/* Email */}
          <div className="mb-1">
            <div className="relative">
              <div
                className={cn(
                  'absolute inset-0 pointer-events-none border rounded-md',
                  shouldFloat('email') ? 'border-primary' : 'border-input',
                  errors.email && 'border-red-500',
                )}
              >
                <span
                  className={cn(
                    'absolute px-1 bg-white text-xs transition-all duration-200 z-10',
                    shouldFloat('email')
                      ? '-top-2 left-3 text-xxs text-primary'
                      : 'top-1/2 left-3 -translate-y-1/2 text-[color:var(--color-placeholders)]',
                    errors.email && (shouldFloat('email') ? 'text-red-500' : 'text-[color:var(--color-placeholders)]'),
                  )}
                >
                  {t('EMAIL_ADDRESS')}
                </span>
              </div>
              <Input
                id="email"
                type="email"
                disabled={isEditMode}
                {...register('email')}
                className="h-11 placeholder:opacity-0 bg-transparent border-0 focus:ring-0 focus:border-0"
                onFocus={() => handleFocus('email')}
                onBlur={() => handleBlur('email')}
                data-testid="Email"
              />
            </div>
            {errors.email && <p className="text-xs text-red-500 mt-1 ml-1">{errors.email.message}</p>}
          </div>

          {/* Department */}
          <div className="mb-1">
            <div className="relative h-11">
              {' '}
              {/* Ensure height consistency */}
              {/* Floating label border */}
              <div
                className={cn(
                  'absolute inset-0 pointer-events-none border rounded-md h-11',
                  shouldFloat('departmentId') ? 'border-primary' : 'border-input',
                  errors.departmentId && 'border-red-500',
                )}
              />
              {/* Floating label */}
              <span
                className={cn(
                  'absolute px-1 bg-white text-xs transition-all duration-200 z-0',
                  shouldFloat('departmentId')
                    ? '-top-2 left-3 text-xxs text-primary'
                    : 'top-1/2 left-3 -translate-y-1/2 text-[color:var(--color-placeholders)]',
                  errors.departmentId &&
                    (shouldFloat('departmentId') ? 'text-red-500' : 'text-[color:var(--color-placeholders)]'),
                )}
              >
                {t('DEPARTMENT')}
              </span>
              {/* Actual Select component */}
              <Controller
                control={control}
                name="departmentId"
                render={({field}) => (
                  <Select
                    value={field.value}
                    onValueChange={value => field.onChange(value)}
                    disabled={isDeptLoading}
                    onOpenChange={open => (open ? handleFocus('departmentId') : handleBlur('departmentId'))}
                  >
                    <SelectTrigger
                      className={cn(
                        'w-full h-11 bg-transparent border-none pl-3 text-left focus:ring-0 focus:outline-none appearance-none',
                        'flex items-center justify-between',
                        '!h-11',
                      )}
                      data-testid="department-trigger"
                    >
                      <SelectValue placeholder="" />
                    </SelectTrigger>
                    <SelectContent className="z-50">
                      {deptData.map(dept => (
                        <SelectItem key={dept.id} value={dept.id} data-testid={`department-option-${dept.name}`}>
                          {dept.name}
                        </SelectItem>
                      ))}
                      {/* Separator */}
                      <Separator className="my-2" />

                      {/* Toggleable + Add New Department Button */}
                      {!showAddInput ? (
                        <button
                          type="button"
                          onClick={() => setShowAddInput(true)}
                          className="w-full text-primary-accent text-sm px-3 py-2 text-left"
                        >
                          + {t('ADD_NEW_DEPARTMENT')}
                        </button>
                      ) : (
                        <div className="px-3 py-2 flex items-center gap-2">
                          <input
                            type="text"
                            placeholder={t('ENTER_DEPARTMENT_NAME')}
                            className="w-50 h-9 px-2 border rounded-sm text-sm focus:outline-none"
                            value={newDeptName}
                            onChange={e => setNewDeptName(e.target.value)}
                          />
                          <button
                            type="button"
                            className="text-primary-accent text-sm"
                            onClick={() => {
                              void handleAddDepartment();
                            }}
                          >
                            + {t('ADD')}
                          </button>
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Error message */}
            {errors.departmentId && <p className="text-xs text-red-500 mt-1 ml-1">{errors.departmentId.message}</p>}
          </div>
        </div>

        {/* Buttons */}
        <div className="w-full flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={handleClose} className="w-26 h-10 font-normal border">
            {t('CANCEL_ADDING')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitDisabled()}
            className="w-26 h-10 font-normal border"
          >
            {getSubmitButtonLabel()}
          </Button>
        </div>
      </form>
    </div>
  );
}
