import {InboxTabsEnum} from '@/enums';
import type {IInboxChatContent, Message} from '../types';

export const MockApiTabContentDb = [
  {
    tabId: 1,
    tabName: InboxTabsEnum.ACTIVE,
    chatList: [
      {
        channelId: 1,
        recepientId: 1,
        recepientName: '<PERSON>',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Hello!',
          timestamp: '12:30 PM',
          unreadMessageCount: 0,
        },
      },
      {
        channelId: 2,
        recepientId: 2,
        recepientName: '<PERSON> Dr<PERSON>',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Hi!',
          timestamp: '11:30 AM',
          unreadMessageCount: 1,
        },
      },
      {
        channelId: 3,
        recepientId: 3,
        recepientName: '<PERSON> Coyne',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Hello',
          timestamp: '11:20 AM',
          unreadMessageCount: 5,
          requestLabel: 'Chat Request',
        },
      },
      {
        channelId: 4,
        recepientId: 4,
        recepientName: 'Adam Zones',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'You: Hello how can I help you?',
          timestamp: '11:10 AM',
          unreadMessageCount: 0,
        },
      },
      {
        channelId: 5,
        recepientId: 5,
        recepientName: 'Josh Homme',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Hello! I need help!',
          timestamp: '11:00 AM',
          unreadMessageCount: 5,
          requestLabel: 'Chat Request',
        },
      },
    ],
  },
  {
    tabId: 2,
    tabName: InboxTabsEnum.QUEUED,
    chatList: [
      {
        channelId: 7,
        recepientId: 7,
        recepientName: 'Amy Holt',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Yes, my issue is resolved.',
          timestamp: '11:20 AM',
          unreadMessageCount: 5,
        },
      },
      {
        channelId: 8,
        recepientId: 8,
        recepientName: 'Raymond Pearson',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Good day!',
          timestamp: '11:10 AM',
          unreadMessageCount: 5,
        },
      },
      {
        channelId: 5,
        recepientId: 5,
        recepientName: 'Jessica Specter Diaz',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Thank you!',
          timestamp: '11:00 AM',
          unreadMessageCount: 5,
        },
      },
    ],
  },
  {
    tabId: 3,
    tabName: InboxTabsEnum.ARCHIVED,
    chatList: [],
  },
  {
    tabId: 4,
    tabName: InboxTabsEnum.MISSED,
    chatList: [
      {
        channelId: 13,
        recepientId: 13,
        recepientName: 'Ross Zane',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'Hello!',
          timestamp: '12:30 PM',
          unreadMessageCount: 2,
        },
      },
      {
        channelId: 12,
        recepientId: 12,
        recepientName: 'Joey Santiago',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'I need assistance!',
          timestamp: '11:30 AM',
          unreadMessageCount: 0,
        },
      },
      {
        channelId: 11,
        recepientId: 11,
        recepientName: 'Marks Holt',
        recepientProfilrUrl: '',
        latestMessageInfo: {
          message: 'I have a billing issue!',
          timestamp: '11:00 AM',
          unreadMessageCount: 5,
        },
      },
    ],
  },
] as IInboxChatContent[];

export const prevMessages = [
  {text: 'Hello!', time: '12:30 PM', sender: 'left', id: 1},
  {text: 'Hello! How can I assist you today?', time: '12:35 PM', sender: 'right', id: 2},
] as Message[];

export const messagesData = [
  {text: 'Hello!', time: '12:30 PM', sender: 'left', id: 1},
  {text: 'Hello! How can I assist you today?', time: '12:35 PM', sender: 'right', id: 2},
] as Message[];
