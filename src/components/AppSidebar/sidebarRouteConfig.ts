import {MESSAGES} from '@/constants/messages.constant';
import {RouteConstant} from '@/constants/route.constant';
import {ChartPie, MessageCircle, Users, BookText, SatelliteDish, FileClock, Settings, Bell} from 'lucide-react';

// Menu items.
interface SidebarItem {
  id: string; // Added unique ID
  title?: string;
  url?: string;
  icon?: React.ComponentType;
  isSeparator?: boolean;
}

export const sidebarItems: SidebarItem[] = [
  {
    id: 'separator-1',
    isSeparator: true,
  },
  {
    id: 'reports',
    title: MESSAGES.SIDEBAR_MENU_REPORTS,
    url: '#',
    icon: ChartPie,
  },
  {
    id: 'separator-2',
    isSeparator: true,
  },
  {
    id: 'chats',
    title: MESSAGES.SIDEBAR_MENU_INBOX,
    url: RouteConstant.INBOX,
    icon: MessageCircle,
  },
  {
    id: 'teams',
    title: MESSAGES.SIDEBAR_MENU_TEAMS,
    url: RouteConstant.TEAMS,
    icon: Users,
  },
  {
    id: 'knowledge-base',
    title: MESSAGES.SIDEBAR_MENU_KNOWLEDGE_BASE,
    url: '#',
    icon: BookText,
  },
  {
    id: 'integrations',
    title: MESSAGES.SIDEBAR_MENU_INTEGRATIONS,
    url: '#',
    icon: SatelliteDish,
  },
  {
    id: 'activity-log',
    title: MESSAGES.SIDEBAR_MENU_ACTIVITY_LOG,
    url: '#',
    icon: FileClock,
  },
  {
    id: 'separator-3',
    isSeparator: true,
  },
  {
    id: 'settings',
    title: MESSAGES.SIDEBAR_MENU_SETTINGS,
    url: RouteConstant.SETTINGS,
    icon: Settings,
  },
  {
    id: 'notifications',
    title: MESSAGES.SIDEBAR_MENU_NOTIFICATIONS,
    url: '#',
    icon: Bell,
  },
];
