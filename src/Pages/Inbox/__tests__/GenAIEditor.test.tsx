import {render, screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {GenAIEditor} from '../components/editors/GenAIEditor';
import {MESSAGES} from '@/constants';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock components
vi.mock('@/components/ui', () => ({
  Button: ({children, variant, className, onClick}: any) => (
    <button data-testid="mock-button" className={`${variant} ${className}`} onClick={onClick}>
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/input', () => ({
  Input: ({value, placeholder, className, onChange}: any) => (
    <input data-testid="mock-input" placeholder={placeholder} className={className} value={value} onChange={onChange} />
  ),
}));

vi.mock('lucide-react', () => ({
  WandSparkles: () => <div data-testid="mock-wand-sparkles-icon" />,
  Languages: () => <div data-testid="mock-languages-icon" />,
  RefreshCcw: () => <div data-testid="mock-refresh-icon" />,
}));

vi.mock('../components/InboxChatSuggestions', () => ({
  InboxChatSuggestion: ({suggestion, onSuggestionClick}: any) => (
    <div data-testid="chat-suggestion" onClick={() => onSuggestionClick(suggestion)}>
      {suggestion}
    </div>
  ),
}));

describe('GenAIEditor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders the GenAIEditor component with all elements', () => {
    render(<GenAIEditor />);

    // Check for main container
    expect(screen.getByTestId('mock-input')).toBeInTheDocument();

    // Check for buttons
    expect(screen.getByText(MESSAGES.MAGIC_WRITE)).toBeInTheDocument();
    expect(screen.getByText(MESSAGES.TRANSLATION)).toBeInTheDocument();
    expect(screen.getByText(MESSAGES.REGENERATE)).toBeInTheDocument();

    // Check for icons
    expect(screen.getByTestId('mock-wand-sparkles-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-languages-icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-refresh-icon')).toBeInTheDocument();

    // Check for suggestions
    const suggestions = screen.getAllByTestId('chat-suggestion');
    expect(suggestions.length).toBe(2);
    expect(suggestions[0].textContent).toBe('Hello! How can I assist you today?');
    expect(suggestions[1].textContent).toBe('Hello there!');
  });

  test('updates input value when typing', () => {
    render(<GenAIEditor />);

    const input = screen.getByTestId('mock-input');
    fireEvent.change(input, {target: {value: 'Test message'}});

    expect(input).toHaveValue('Test message');
  });

  test('updates input value when clicking on a suggestion', () => {
    render(<GenAIEditor />);

    const suggestions = screen.getAllByTestId('chat-suggestion');
    fireEvent.click(suggestions[0]);

    const input = screen.getByTestId('mock-input');
    expect(input).toHaveValue('Hello! How can I assist you today?');
  });

  test('clears input when clicking regenerate button', () => {
    render(<GenAIEditor />);

    // First set some value
    const input = screen.getByTestId('mock-input');
    fireEvent.change(input, {target: {value: 'Test message'}});
    expect(input).toHaveValue('Test message');

    // Click regenerate button
    const regenerateButton = screen.getByText(MESSAGES.REGENERATE);
    fireEvent.click(regenerateButton);

    // Check if input is cleared
    expect(input).toHaveValue('');
  });
});
