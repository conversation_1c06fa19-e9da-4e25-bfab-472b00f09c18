import {render, screen, fireEvent} from '@testing-library/react';
import {describe, it, expect, vi, beforeEach} from 'vitest';
import InboxChatEmojiModal from '../components/InboxChatEmojiModal';

// Mock emoji-picker-react
vi.mock('emoji-picker-react', () => ({
  __esModule: true,
  default: ({onEmojiClick}: any) => (
    <div data-testid="mock-emoji-picker">
      <button data-testid="mock-emoji" onClick={() => onEmojiClick({emoji: '😀'}, {})}>
        😀
      </button>
    </div>
  ),
}));

describe('InboxChatEmojiModal', () => {
  const onClose = vi.fn();
  const onEmojiClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('does not render when show is false', () => {
    const {container} = render(<InboxChatEmojiModal show={false} onClose={onClose} onEmojiClick={onEmojiClick} />);
    expect(container.firstChild).toBeNull();
  });

  it('calls onEmojiClick when emoji is clicked', () => {
    render(<InboxChatEmojiModal show={true} onClose={onClose} onEmojiClick={onEmojiClick} />);
    fireEvent.click(screen.getByTestId('mock-emoji'));
    expect(onEmojiClick).toHaveBeenCalledWith({emoji: '😀'}, {});
  });
  it('calls onClose when clicking outside modal and toggle button', () => {
    const onClose = vi.fn();
    const onEmojiClick = vi.fn();
    const toggleButton = document.createElement('button');
    document.body.appendChild(toggleButton);
    const toggleButtonRef = {current: toggleButton};

    // Render the modal
    render(
      <InboxChatEmojiModal
        show={true}
        onClose={onClose}
        onEmojiClick={onEmojiClick}
        toggleButtonRef={toggleButtonRef}
      />,
    );

    // Simulate clicking outside both modal and toggle button
    fireEvent.mouseDown(document.body);

    expect(onClose).toHaveBeenCalled();
  });
});
