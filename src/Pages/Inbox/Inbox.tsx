import {Tabs} from '@/components/ui/tab';
import {InboxTabsEnum} from '@/enums';

import './inbox.css';
import {useState} from 'react';
import {useTranslation} from 'react-i18next';

import {MockApiTabData} from './mockData/mock-tab-data';
import type {TabData} from './types';
import {InboxTabContent, InboxTopbar} from './components';
import {MESSAGES} from '@/constants';

const Inbox = () => {
  const [activeTab, setActiveTab] = useState<InboxTabsEnum>(InboxTabsEnum.ACTIVE);
  const {t} = useTranslation();

  return (
    <div className="relative w-full h-full font-sans mb-[25px]">
      <div className="absolute top-0 left-0 right-0  flex flex-col overflow-auto h-full">
        <h1 className="font-medium text-[20px] leading-[30px] text-[var(--text-gray-color)] tracking-[0.8px] capitalize opacity-100  mb-4">
          {t(MESSAGES.INBOX_HEADING_CHATS)}
        </h1>

        <Tabs
          defaultValue={InboxTabsEnum.ACTIVE}
          className="w-full flex flex-col flex-1 min-h-0"
          onValueChange={(val: string) => setActiveTab(val)}
        >
          {/**Inbox Tab Top Bar */}
          <InboxTopbar tabsData={MockApiTabData} />
          {/**Inbox Tab Content*/}
          <InboxTabContent tabData={MockApiTabData.find(tab => tab.tabName === activeTab) ?? ({} as TabData)} />
        </Tabs>
      </div>
    </div>
  );
};
export default Inbox;
