import {ToastColor} from '@/types';
import {useSnackbar, type OptionsObject} from 'notistack';

interface ToastOptions {
  title?: string;
  description: string;
  duration?: number;
  color?: ToastColor;
  anchorOrigin?: OptionsObject['anchorOrigin'];
}
export interface CustomSnackbarOptions extends OptionsObject {
  title?: string;
  color?: ToastColor;
}

export function useToast() {
  const {enqueueSnackbar} = useSnackbar();

  const toast = ({
    title,
    description,
    color = ToastColor.Info,
    duration = 300000000,
    anchorOrigin = {vertical: 'top', horizontal: 'right'},
  }: ToastOptions) => {
    const options: CustomSnackbarOptions = {
      variant: 'custom',
      autoHideDuration: duration,
      title,
      color,
      anchorOrigin,
    };

    enqueueSnackbar(description, options);
  };

  return {toast};
}
