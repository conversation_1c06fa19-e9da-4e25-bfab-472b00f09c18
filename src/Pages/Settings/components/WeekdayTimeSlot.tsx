import {TimeSlotRow} from './TimeSlotRow';

interface WeekdayTimeSlotProps {
  dayData: {
    day: string;
    timeSlots: {from: string; to: string; id: string}[];
  };
  dayIndex: number;
  handleAddRow: (dayIndex: number) => void;
  handleRemoveRow: (indexToRemove: number, dayIndex: number) => void;
}

export const WeekdayTimeSlot = ({dayData, dayIndex, handleAddRow, handleRemoveRow}: WeekdayTimeSlotProps) => {
  return (
    <div key={dayData.day} className="flex items-start justify-start py-2 border-b border-gray-200 last:border-b-0">
      <label className="flex items-center space-x-2 w-45 text-sm font-medium text-gray-700 pt-2 shrink-0">
        <input
          type="checkbox"
          className="form-checkbox w-5.5 h-5.5 bg-white border border-solid border-gray-300 rounded-sm"
        />
        <span>{dayData.day}</span>
      </label>

      {/* Flexible time slot container */}
      <div className="flex flex-col space-y-2 space-x-1 flex-1">
        {dayData.timeSlots.map((slot, slotIndex) => (
          <TimeSlotRow
            key={slot.from}
            slot={slot}
            slotIndex={slotIndex}
            totalSlots={dayData.timeSlots.length}
            onAddRow={() => handleAddRow(dayIndex)}
            onRemoveRow={() => handleRemoveRow(slotIndex, dayIndex)}
            canAdd={dayData.timeSlots.length < 3}
            canRemove={dayData.timeSlots.length > 1}
          />
        ))}
      </div>
    </div>
  );
};
