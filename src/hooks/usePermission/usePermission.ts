import type {PermissionsEnum} from '../../enums';
import {selectCurrentPermissions} from '../../redux/auth/authSlice';
import {hasPermission} from '../../utils';
import {useAppSelector} from '../redux-hooks';

export const usePermission = (requiredPermissions?: PermissionsEnum[]) => {
  const userPermissions = useAppSelector(selectCurrentPermissions);

  return !userPermissions || hasPermission(userPermissions, requiredPermissions);
};
