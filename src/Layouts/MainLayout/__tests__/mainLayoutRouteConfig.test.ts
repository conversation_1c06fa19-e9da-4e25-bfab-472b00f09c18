import {describe, expect, test, vi} from 'vitest';
import mainLayoutRouteConfig from '../mainLayoutRouteConfig';
import {RouteConstant} from '../../../constants';

// Mock the lazy-loaded components
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    lazy: vi.fn().mockImplementation(importFn => {
      return importFn();
    }),
  };
});

// Mock the imported components
vi.mock('@/Pages/Home/Home', () => ({
  default: () => null,
}));

vi.mock('@/Pages/Teams/TeamsPage', () => ({
  default: () => null,
}));

vi.mock('@/Pages/Agents/AddAgentPage', () => ({
  default: () => null,
}));

vi.mock('@/Pages/Inbox/Inbox', () => ({
  default: () => null,
}));

describe('mainLayoutRouteConfig', () => {
  test('should export an array of route configurations', () => {
    expect(Array.isArray(mainLayoutRouteConfig)).toBe(true);
    expect(mainLayoutRouteConfig.length).toBeGreaterThan(0);
  });

  test('should contain route for home page', () => {
    const homeRoute = mainLayoutRouteConfig.find(route => route.path === RouteConstant.HOME);
    expect(homeRoute).toBeDefined();
    expect(homeRoute?.path).toBe(RouteConstant.HOME);
    expect(homeRoute?.component).toBeDefined();
  });

  test('should contain route for teams page', () => {
    const teamsRoute = mainLayoutRouteConfig.find(route => route.path === RouteConstant.TEAMS);
    expect(teamsRoute).toBeDefined();
    expect(teamsRoute?.path).toBe(RouteConstant.TEAMS);
    expect(teamsRoute?.component).toBeDefined();
  });

  test('should contain route for add agent page', () => {
    const agentsRoute = mainLayoutRouteConfig.find(route => route.path === RouteConstant.ADD_AGENT);
    expect(agentsRoute).toBeDefined();
    expect(agentsRoute?.path).toBe(RouteConstant.ADD_AGENT);
    expect(agentsRoute?.component).toBeDefined();
  });

  test('should contain route for inbox page', () => {
    const inboxRoute = mainLayoutRouteConfig.find(route => route.path === RouteConstant.INBOX);
    expect(inboxRoute).toBeDefined();
    expect(inboxRoute?.path).toBe(RouteConstant.INBOX);
    expect(inboxRoute?.component).toBeDefined();
  });

  test('all routes should have valid path and component properties', () => {
    mainLayoutRouteConfig.forEach(route => {
      expect(route).toHaveProperty('path');
      expect(route).toHaveProperty('component');
      expect(typeof route.path).toBe('string');
      expect(route.component).toBeDefined();
    });
  });
});
