'use client';
import {cn} from '@/lib/utils';
import {Ta<PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger} from '@/components/ui/tabs';
import AgentsPage from '@/Pages/Agents/AgentsPage';
import DepartmentPage from '@/Pages/Departments/DepartmentPage';
import {useTranslation} from 'react-i18next';

const TeamsPage = () => {
  const {t} = useTranslation();
  return (
    <div className="w-full">
      <h2 className="text-xl font-medium">{t('TEAM')}</h2>
      <Tabs defaultValue="agent" className="w-full mt-4">
        <TabsList className="flex justify-start bg-transparent space-x-4 mb-0">
          <TabsTrigger
            value="agent"
            className={cn(
              'bg-transparent',
              'w-auto',
              'border-0 rounded-none shadow-transparent',
              'hover:cursor-pointer',
              'focus:outline-none',
              'border-b-2 border-transparent',
              'text-muted-foreground',
              'data-[state=active]:text-foreground',
              'data-[state=active]:bg-transparent',
              'data-[state=active]:border-blue-500',
              'p-4',
              'justify-start',
              'text-left',
            )}
          >
            {t('AGENT')}
          </TabsTrigger>
          <TabsTrigger
            value="departments"
            className={cn(
              'bg-transparent',
              'w-auto',
              'border-0 rounded-none shadow-transparent',
              'hover:cursor-pointer',
              'focus:outline-none',
              'border-b-2 border-transparent',
              'text-muted-foreground',
              'data-[state=active]:text-foreground',
              'data-[state=active]:bg-transparent',
              'data-[state=active]:border-blue-500',
              'p-4',
              'justify-start',
              'text-left',
            )}
          >
            {t('DEPARTMENTS')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="agent">
          <AgentsPage />
        </TabsContent>
        <TabsContent value="departments">
          <DepartmentPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TeamsPage;
