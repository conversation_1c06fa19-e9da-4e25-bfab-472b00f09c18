'use client;';

import {useNavigate} from 'react-router-dom';
import {Button} from '@/components/ui/button';
import {Pencil, Trash2} from 'lucide-react';
import type {Row} from '@tanstack/react-table';
import {type AgentsDetails} from '@/types';

interface ActionsCellProps {
  row: Row<AgentsDetails>;
}

const ActionsCell: React.FC<ActionsCellProps> = ({row}) => {
  const navigate = useNavigate();

  const handleEdit = () => {
    const agentId = row.original.id; // ✅ now properly typed
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    navigate(`/addAgent?id=${agentId}`);
  };

  return (
    <div className="flex items-center justify-center gap-0 h-full">
      <Button variant="ghost" size="icon" aria-label="Edit" onClick={handleEdit}>
        <Pencil className="h-2 w-2 text-gray-500" />
      </Button>
      <Button variant="ghost" size="icon" aria-label="Delete">
        <Trash2 className="h-2 w-2 text-gray-500" />
      </Button>
    </div>
  );
};

export default ActionsCell;
