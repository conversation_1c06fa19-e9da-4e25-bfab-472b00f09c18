import {createSlice, type PayloadAction} from '@reduxjs/toolkit';
import type {RootState} from '../store';
import type {PermissionsEnum} from '../../enums';
import {
  getAccessToken,
  getRefreshToken,
  getTokenExpiration,
  storeAccessToken,
  storeRefreshToken,
  storeTokenExpiration,
  clearTokens,
} from '../../utils/tokenHelper';

export interface AuthState {
  isLoggedIn: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
  permissions: PermissionsEnum[] | null;
}

export interface AuthResData {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  'not-before-policy': number;
  session_state: string;
  scope: string;
}

const initialState: AuthState = {
  accessToken: getAccessToken(),
  refreshToken: getRefreshToken(),
  expires: getTokenExpiration(),
  isLoggedIn: !!getAccessToken(),
  permissions: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const {access_token, refresh_token, expires_in} = action.payload;

      // Store tokens securely using tokenHelper
      storeAccessToken(access_token);
      storeRefreshToken(refresh_token);
      storeTokenExpiration(expires_in);

      // Update state
      state.accessToken = access_token;
      state.refreshToken = refresh_token;
      state.expires = Date.now() + expires_in * 1000; // Convert to timestamp
      state.isLoggedIn = true;
    },
    unsetCredentials: state => {
      // Clear tokens from storage
      clearTokens();

      // Update state
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
      state.permissions = null;
    },
    setPermissions: (state, action: PayloadAction<PermissionsEnum[]>) => {
      state.permissions = action.payload;
    },
  },
});

export const {setCredentials, unsetCredentials, setPermissions} = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) => state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) => state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) => state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
export const selectCurrentPermissions = (state: RootState) => state.auth.permissions;
