import {render, screen} from '@testing-library/react';
import {describe, it, expect, vi} from 'vitest';
import {MemoryRouter} from 'react-router-dom';
import Routes from '../Routes';
import {type RouteObject} from 'react-router-dom';

// Mock the useRoutes hook
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useRoutes: vi.fn(() => <div data-testid="mocked-routes">Mocked Routes</div>),
  };
});

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => (key === 'MESSAGES.GLOBAL_LOADING_FALLBACK' ? 'Loading...' : key),
  }),
}));

describe('Routes', () => {
  const mockRoutesConfig: RouteObject[] = [
    {
      path: '/',
      element: <div>Home Page</div>,
    },
    {
      path: '/test',
      element: <div>Test Page</div>,
    },
  ];

  it('renders routes with suspense fallback', () => {
    render(
      <MemoryRouter>
        <Routes routesConfig={mockRoutesConfig} />
      </MemoryRouter>,
    );

    expect(screen.getByTestId('mocked-routes')).toBeInTheDocument();
  });
});
