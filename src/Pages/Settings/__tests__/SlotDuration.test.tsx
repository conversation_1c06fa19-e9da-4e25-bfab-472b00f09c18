import {render, screen, fireEvent} from '@testing-library/react';
import {SlotDuration} from '../components/SlotDuration';
import {TimeSlotRow} from '../components/TimeSlotRow';
import {WeekdayTimeSlot} from '../components/WeekdayTimeSlot';
import {vi, describe, test, expect, beforeEach} from 'vitest';

// Mock child components
vi.mock('../components/TimeSlotRow', () => ({
  TimeSlotRow: vi.fn(({onAddRow, onRemoveRow, slotIndex}) => (
    <div data-testid={`time-slot-row-${slotIndex}`}>
      Time Slot Row {slotIndex}
      <button onClick={onAddRow} data-testid={`add-row-button-${slotIndex}`}>
        Add
      </button>
      <button onClick={onRemoveRow} data-testid={`remove-row-button-${slotIndex}`}>
        Remove
      </button>
    </div>
  )),
}));

vi.mock('../components/WeekdayTimeSlot', () => ({
  WeekdayTimeSlot: vi.fn(({dayData, dayIndex, handleAddRow, handleRemoveRow}) => (
    <div data-testid={`weekday-time-slot-${dayData.day}`}>
      {dayData.day}
      <button onClick={() => handleAddRow(dayIndex)} data-testid={`add-weekday-row-button-${dayIndex}`}>
        Add Weekday
      </button>
      <button onClick={() => handleRemoveRow(0, dayIndex)} data-testid={`remove-weekday-row-button-${dayIndex}`}>
        Remove Weekday
      </button>
    </div>
  )),
}));

describe('SlotDuration Component', () => {
  beforeEach(() => {
    // Clear mocks before each test
    (TimeSlotRow as any).mockClear();
    (WeekdayTimeSlot as any).mockClear();
  });

  test('renders TimeSlotRow when showWeekdays is false (default)', () => {
    render(<SlotDuration />);
    expect(TimeSlotRow).toHaveBeenCalledTimes(1);
    expect(WeekdayTimeSlot).not.toHaveBeenCalled();
    expect(screen.getByTestId('time-slot-row-0')).toBeInTheDocument();
  });

  test('renders WeekdayTimeSlot when showWeekdays is true', () => {
    render(<SlotDuration showWeekdays={true} />);
    expect(WeekdayTimeSlot).toHaveBeenCalledTimes(7); // For each day of the week
    expect(TimeSlotRow).not.toHaveBeenCalled();
    expect(screen.getByTestId('weekday-time-slot-Monday')).toBeInTheDocument();
    expect(screen.getByTestId('weekday-time-slot-Sunday')).toBeInTheDocument();
  });

  test('adds a new time slot row when "Add" button is clicked (non-weekday mode)', () => {
    render(<SlotDuration />);
    fireEvent.click(screen.getByTestId('add-row-button-0'));
    expect(TimeSlotRow).toHaveBeenCalledTimes(3); // Initial + 2 more renders for 2 slots
    expect(screen.getByTestId('time-slot-row-1')).toBeInTheDocument();
  });

  test('does not add more than 3 time slot rows (non-weekday mode)', () => {
    render(<SlotDuration />);
    fireEvent.click(screen.getByTestId('add-row-button-0')); // Add 2nd slot
    fireEvent.click(screen.getByTestId('add-row-button-1')); // Add 3rd slot
    fireEvent.click(screen.getByTestId('add-row-button-2')); // Try to add 4th slot
    expect(TimeSlotRow).toHaveBeenCalledTimes(6); // Initial (1) + 2 (for 2nd slot) + 3 (for 3rd slot) = 6 renders in total
    expect(screen.queryByTestId('time-slot-row-3')).not.toBeInTheDocument();
  });

  test('removes a time slot row when "Remove" button is clicked (non-weekday mode)', () => {
    render(<SlotDuration />);
    fireEvent.click(screen.getByTestId('add-row-button-0')); // Add a second slot
    fireEvent.click(screen.getByTestId('remove-row-button-0')); // Remove the first slot
    expect(TimeSlotRow as any).toHaveBeenLastCalledWith(
      expect.objectContaining({
        slotIndex: 0, // The remaining slot should now have index 0
        totalSlots: 1, // There should be only one slot left
      }),
      undefined, // Expect the second argument to be undefined
    );
  });

  test('does not remove the last time slot row (non-weekday mode)', () => {
    render(<SlotDuration />);
    fireEvent.click(screen.getByTestId('remove-row-button-0')); // Try to remove the only slot
    expect(TimeSlotRow).toHaveBeenCalledTimes(1); // Still only one slot rendered
    expect(screen.getByTestId('time-slot-row-0')).toBeInTheDocument();
  });

  test('adds a new time slot for a specific weekday when "Add Weekday" button is clicked', () => {
    render(<SlotDuration showWeekdays={true} />);
    fireEvent.click(screen.getByTestId('add-weekday-row-button-0')); // Add slot for Monday
    // This is harder to test directly without inspecting the internal state of WeekdayTimeSlot.
    // We can infer by checking if WeekdayTimeSlot was called with updated props.
    // For now, we'll rely on the mock to ensure the handler is called.
    expect(WeekdayTimeSlot).toHaveBeenCalled();
  });

  test('removes a time slot for a specific weekday when "Remove Weekday" button is clicked', () => {
    render(<SlotDuration showWeekdays={true} />);
    fireEvent.click(screen.getByTestId('add-weekday-row-button-0')); // Add a second slot for Monday
    fireEvent.click(screen.getByTestId('remove-weekday-row-button-0')); // Remove a slot for Monday
    expect(WeekdayTimeSlot).toHaveBeenCalled();
  });
});
