import {CircleUser, MessagesSquare} from 'lucide-react';
import UserInfo from '../../../assets/inboxPanel/UserInfo.svg';

export const InboxChatSideProfile = () => {
  return (
    <div className="w-12 bg-white flex flex-col items-center pt-6 border-l border-gray-300">
      <CircleUser size={24} className="mb-[30px] text-[var(--color-icon)]" />
      <img src={UserInfo} alt="User Info" className="w-6 h-6 mb-[30px]" />
      <MessagesSquare size={24} className="text-[var(--color-icon)]" />
    </div>
  );
};
