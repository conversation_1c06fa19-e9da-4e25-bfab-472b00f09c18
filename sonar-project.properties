sonar.host.url=http://sonar.comviva.com/sonar
sonar.links.scm=http://blrgitlab.comviva.com/mbs/ng/sf/chatbot/ng-agent-portal-ui.git
sonar.projectVersion=8.0
sonar.sources=./src
sonar.cfamily.build-wrapper-output.bypass=true
sonar.projectName=MCS_CPAAS_8x_RM_Agent-Portal-UI
sonar.projectKey=my:MCS_CPAAS_8x_RM_Agent-Portal-UI
sonar.branch.name=ng-mainline-dev
sonar.exclusions=**/test/**, **/node_modules/**, **/whatfix*/**,app/js/**,app/online_help/**, **/customers*/**,app/css/static/*,scripts/**,app/fonts/font-awesome-4.4.0/**,app/styles/elements/**
