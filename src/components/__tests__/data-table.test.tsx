import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {describe, it, expect, vi, beforeEach} from 'vitest';
import {DataTable} from '../data-table';
import {FilterItemType, type FilterGroup} from '@/types';

// Mock the FilterPanel component
vi.mock('../filters/FilterPanel', () => ({
  FilterPanel: vi.fn(({isOpen, onClose, onApplyFilters}) => {
    if (!isOpen) return null;
    return (
      <div data-testid="mock-filter-panel">
        <button onClick={() => onApplyFilters({status: ['active']})}>Apply Filter</button>
        <button onClick={() => onClose()}>Close</button>
      </div>
    );
  }),
}));

describe('DataTable Filter Section', () => {
  const mockColumns = [
    {id: 'name', header: 'Name', accessorKey: 'name'},
    {id: 'email', header: 'Email', accessorKey: 'email'},
  ];

  const mockData = [
    {id: 1, name: '<PERSON>', email: '<EMAIL>'},
    {id: 2, name: '<PERSON>', email: '<EMAIL>'},
  ];

  const mockFilterGroups: FilterGroup[] = [
    {
      id: 'status',
      name: 'Status',
      items: [
        {id: 'active', label: 'Active', selected: false, type: FilterItemType.CHECKBOX},
        {id: 'inactive', label: 'Inactive', selected: false, type: FilterItemType.CHECKBOX},
      ],
    },
  ];

  const mockHandleApplyFilters = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders filter button when filterGroups are provided', () => {
    render(
      <DataTable
        columns={mockColumns}
        data={mockData}
        filterGroups={mockFilterGroups}
        handleApplyFilters={mockHandleApplyFilters}
      />,
    );

    const filterButton = screen.getByTestId('filter-button');
    expect(filterButton).toBeInTheDocument();
  });

  it('does not render filter button when filterGroups are not provided', () => {
    render(<DataTable columns={mockColumns} data={mockData} />);

    const filterButton = screen.queryByTestId('filter-button');
    expect(filterButton).not.toBeInTheDocument();
  });

  it('opens FilterPanel when filter button is clicked', async () => {
    render(
      <DataTable
        columns={mockColumns}
        data={mockData}
        filterGroups={mockFilterGroups}
        handleApplyFilters={mockHandleApplyFilters}
      />,
    );

    const filterButton = screen.getByTestId('filter-button');
    fireEvent.click(filterButton);

    await waitFor(() => {
      expect(screen.getByTestId('mock-filter-panel')).toBeInTheDocument();
    });
  });

  it('shows indicator dot when active filters are present', () => {
    render(
      <DataTable
        columns={mockColumns}
        data={mockData}
        filterGroups={mockFilterGroups}
        handleApplyFilters={mockHandleApplyFilters}
        activeFilters={{status: ['active']}}
      />,
    );

    const indicator = screen.getByTestId('filter-indicator');
    expect(indicator).toBeInTheDocument();
  });

  it('does not show indicator dot when no active filters', () => {
    render(
      <DataTable
        columns={mockColumns}
        data={mockData}
        filterGroups={mockFilterGroups}
        handleApplyFilters={mockHandleApplyFilters}
        activeFilters={null}
      />,
    );

    const indicator = screen.queryByTestId('filter-indicator');
    expect(indicator).not.toBeInTheDocument();
  });

  it('calls handleApplyFilters when filters are applied', async () => {
    render(
      <DataTable
        columns={mockColumns}
        data={mockData}
        filterGroups={mockFilterGroups}
        handleApplyFilters={mockHandleApplyFilters}
      />,
    );

    // Open filter panel
    const filterButton = screen.getByTestId('filter-button');
    fireEvent.click(filterButton);

    // Apply filter
    const applyButton = await screen.findByText('Apply Filter');
    fireEvent.click(applyButton);

    expect(mockHandleApplyFilters).toHaveBeenCalledWith({status: ['active']});
  });
});
