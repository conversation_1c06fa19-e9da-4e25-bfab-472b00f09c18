import type {ThemeVariables} from '@/types/theme.type';

export class ThemeManager {
  static apply(theme: ThemeVariables): void {
    const root = document.documentElement;
    Object.entries(theme).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value);
    });
  }

  static reset(defaultTheme?: ThemeVariables): void {
    const root = document.documentElement;
    Object.keys(defaultTheme ?? {}).forEach(key => {
      root.style.removeProperty(`--${key}`);
    });
  }

  static persist(theme: ThemeVariables): void {
    localStorage.setItem('theme', JSON.stringify(theme));
  }

  static load(): ThemeVariables | null {
    const stored = localStorage.getItem('theme');
    return stored ? (JSON.parse(stored) as ThemeVariables) : null;
  }

  static applyStored(): void {
    const theme = this.load();
    if (theme) this.apply(theme);
  }
}
