import {Input} from '@/components/ui/input';
import {Clock, SquarePlus, Trash2} from 'lucide-react';

interface TimeSlotRowProps {
  slot: {from: string; to: string; id: string};
  slotIndex: number;
  totalSlots: number;
  onAddRow: () => void;
  onRemoveRow: () => void;
  canAdd: boolean;
  canRemove: boolean;
}

export const TimeSlotRow = ({
  slot,
  slotIndex,
  totalSlots,
  onAddRow,
  onRemoveRow,
  canAdd,
  canRemove,
}: TimeSlotRowProps) => {
  return (
    <div className="flex items-center justify-between pl-2">
      <div className="flex items-center space-x-2">
        {/* From Time */}
        <div className="relative">
          <Input
            type="text"
            defaultValue={slot.from}
            className="w-46.5 h-8.5 rounded-md border border-gray-300 px-4 text-sm font-[Poppins] text-black"
          />
          <Clock className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        </div>

        <span className="text-[18px] text-gray-700 px-1">-</span>

        {/* To Time */}
        <div className="relative">
          <Input
            type="text"
            defaultValue={slot.to}
            className="w-46.5 h-8.5 rounded-md border border-gray-300 px-4 text-sm font-[Poppins] text-black"
          />
          <Clock className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        </div>
      </div>

      {/* Right icon container */}
      <div className="w-8 h-8.5 flex items-center justify-center">
        {canAdd && slotIndex === totalSlots - 1 ? (
          <SquarePlus
            data-testid="SquarePlusIcon"
            className="w-5 h-5 text-[var(--primary)] cursor-pointer"
            onClick={onAddRow}
          />
        ) : (
          <Trash2
            data-testid="Trash2Icon"
            className={`w-4 h-4 text-gray-500 ${canRemove ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}`}
            onClick={() => canRemove && onRemoveRow()}
          />
        )}
      </div>
    </div>
  );
};
