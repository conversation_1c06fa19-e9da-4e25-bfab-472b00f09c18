import {CommonBreadcrumb} from '@/components/BreadCrumbs/CommonBreadCrumbs';
import {Tabs, Ta<PERSON>Content, TabsList, TabsTrigger} from '@/components/ui/tabs';
import {Button} from '@/components/ui/button';
import '../settings.css';
import {ConfiguredSlots} from '../components';
import {useTranslation} from 'react-i18next';
import {MESSAGES, RouteConstant} from '@/constants';
import {useNavigate} from 'react-router-dom';

const BusinessHours = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  return (
    <div className="min-h-screen">
      <div className="pr-4 pb-0">
        <CommonBreadcrumb
          items={[{label: t(MESSAGES.SIDEBAR_MENU_SETTINGS), href: '/settings'}, {label: t(MESSAGES.BUSINESS_HOURS)}]}
        />
      </div>
      <div className="mt-7.5 md:ml-27.5 w-245">
        <h2 className="font-medium text-base leading-6.5 text-black mb-4 w-245">{t(MESSAGES.SET_UP_BUSINESS_HOURS)}</h2>
        <Tabs defaultValue="time-setup" className="w-245">
          <TabsList className="business-hours-tabs-list p-0">
            <TabsTrigger value="time-setup" className="business-hours-tabs-trigger">
              {t(MESSAGES.TIME_SETUP)}
            </TabsTrigger>
            <TabsTrigger value="holidays" className="business-hours-tabs-trigger" disabled>
              {t(MESSAGES.HOLIDAYS)}
            </TabsTrigger>
          </TabsList>
          <div className="h-px bg-gray-300 w-245 relative mt-[-2px]"></div>
          <TabsContent value="time-setup">
            <div className="mt-2 w-245">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-normal leading-5 my-0">{t(MESSAGES.CONFIGURED_SLOTS)}</h2>{' '}
                <Button
                  variant="link"
                  className="text-[var(--button-blue)] font-normal font-sans leading-5.5 no-underline hover:no-underline focus:no-underline"
                  onClick={() => void navigate(RouteConstant.ADD_BUSINESS_HOURS)}
                >
                  {t(MESSAGES.ADD_CUSTOM_HOURS)}
                </Button>
              </div>
              <ConfiguredSlots />
            </div>
          </TabsContent>
          <TabsContent value="holidays">{/* Content for Holidays tab */}</TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default BusinessHours;
