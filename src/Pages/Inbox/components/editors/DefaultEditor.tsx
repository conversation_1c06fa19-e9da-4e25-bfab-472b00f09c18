import {MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';

interface Props {
  inputValue: string;
  setInputValue: (value: string) => void;
}
export const DefaultEditor = ({inputValue, setInputValue}: Props) => {
  const {t} = useTranslation();
  return (
    <div className="border border-[var(--border-gray-color)] rounded-xl mx-5 h-15">
      <div className="h-[100%] w-[70%] flex items-center pt-5">
        <textarea
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          placeholder={t(MESSAGES.CHAT_INPUT_PLACEHOLDER)}
          className="bg-white border-none outline-none focus:border-none focus:outline-none active:border-none active:outline-none rounded-xl resize-none w-[100%] px-4 text-sm placeholder:text-[var(--placeholder-gray)] h-[100%]"
        />
      </div>
    </div>
  );
};
