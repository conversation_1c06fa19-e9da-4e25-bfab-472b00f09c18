import {useState} from 'react';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {Badge} from '@/components/ui/badge';
import type {InboxChatListContentProps} from '../types/inbox-component-props';

export const InboxChatList: React.FC<InboxChatListContentProps> = ({chatListContent}) => {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <div className="overflow-auto flex-1 min-h-0 scroll-pb-2">
      {chatListContent.chatList?.map((chat, index) => (
        <div
          key={chat.channelId}
          onClick={() => setSelectedIndex(index)}
          className={`chat-item ${selectedIndex === index ? 'selected' : ''}`}
        >
          <Avatar>
            <AvatarFallback className="text-[12px] font-medium font-sans">
              {getInitials(chat.recepientName)}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 flex flex-col">
            <div className="flex justify-between items-center">
              <span className="font-medium truncate max-w-35 text-sm" title={chat.recepientName}>
                {chat.recepientName}
              </span>

              <span className="text-xs text-muted-foreground whitespace-nowrap mt-1">
                {chat.latestMessageInfo.timestamp}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <p
                className={`text-sm truncate max-w-[100px] ${
                  chat.latestMessageInfo.requestLabel || chat.latestMessageInfo.unreadMessageCount
                    ? 'font-medium'
                    : 'font-normal text-[var(--color-muted-text)]'
                }`}
              >
                {chat.latestMessageInfo.message}
              </p>
              {chat.latestMessageInfo.unreadMessageCount > 0 && (
                <Badge className="badge-chat-list-count font-normal">
                  {chat.latestMessageInfo.requestLabel || chat.latestMessageInfo.unreadMessageCount}
                </Badge>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
