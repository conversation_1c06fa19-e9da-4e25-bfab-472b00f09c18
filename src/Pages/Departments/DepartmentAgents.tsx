import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import type {AgentsDetails} from '@/types';
type Props = {
  agents: AgentsDetails[];
};
export const DepartmentAgents = ({agents}: Props) => {
  const maxVisible = 5;
  const visibleAgents = agents.slice(0, maxVisible);
  const remainingCount = agents.length - maxVisible;

  const getInitials = (name = '') => {
    const [first = '', last = ''] = name.trim().split(' ');
    return `${first[0] || ''}${last[0] || ''}`.toUpperCase();
  };
  return (
    <div className="*:data-[slot=avatar]:ring-background flex -space-x-2 *:data-[slot=avatar]:ring-2 *:data-[slot=avatar]:grayscale">
      {visibleAgents.map(agent => (
        <Avatar key={agent.email}>
          <AvatarFallback className="text-[#4E4E4E]">{getInitials(agent.name)}</AvatarFallback>
        </Avatar>
      ))}
      {remainingCount > 0 && (
        <Avatar>
          <AvatarFallback>+{remainingCount}</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};
