'use client';

import type {ColumnDef} from '@tanstack/react-table';
import {Button} from '../../components/ui/button';
import {Pencil, Trash2} from 'lucide-react';
import type {IDepartment} from '../../types';
import {formatDate} from '@/utils';
import {DepartmentAgents} from './DepartmentAgents';
import type {TFunction} from 'i18next';

export const departmentColumns = (t: TFunction<'translation', undefined>): ColumnDef<IDepartment>[] => {
  return [
    {
      accessorKey: 'name',
      header: t('NAME'),
      id: 'name',
    },
    {
      accessorKey: 'agents',
      header: t('DEPARTMENT_AGENTS'),
      id: 'agents',
      cell: ({row}) => {
        return <DepartmentAgents agents={row.getValue('agents') ?? []} />;
      },
    },
    {
      accessorKey: 'createdAt',
      id: 'createdAt',
      header: t('CREATED_ON'),
      cell: ({row}) => {
        return formatDate(row.getValue('createdAt'));
      },
    },
    {
      id: 'actions',
      header: t('ACTIONS'),
      cell: () => {
        return (
          <div className="flex items-center justify-center gap-0 h-full">
            <Button variant="ghost" size="icon" aria-label={t('EDIT')}>
              <Pencil className="h-2 w-2 text-gray-500" />
            </Button>
            <Button variant="ghost" size="icon" aria-label={t('DELETE')}>
              <Trash2 className="h-2 w-2 text-gray-500" />
            </Button>
          </div>
        );
      },
    },
  ];
};
