import {render, screen} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxSidebarHeader} from '../components/InboxSidebarHeader';

// Mock TabsList and TabsTrigger components
vi.mock('@/components/ui/tabs', async () => {
  const actual = await vi.importActual('@/components/ui/tabs');
  return {
    ...actual,
    TabsList: ({children, className}: {children: React.ReactNode; className: string}) => (
      <div data-testid="mock-tabs-list" className={className}>
        {children}
      </div>
    ),
    TabsTrigger: ({value, children, className}: {value: string; children: React.ReactNode; className: string}) => (
      <button data-testid={`mock-tabs-trigger-${value}`} className={className}>
        {children}
      </button>
    ),
  };
});

describe('InboxSidebarHeader', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders TabsList component', () => {
    render(<InboxSidebarHeader />);
    expect(screen.getByTestId('mock-tabs-list')).toBeInTheDocument();
  });

  test('renders "Self" and "Other Agents" tabs', () => {
    render(<InboxSidebarHeader />);
    const selfTab = screen.getByRole('button', {name: 'INBOX_TAB_SELF'});
    const otherAgentsTab = screen.getByRole('button', {name: 'INBOX_TAB_OTHER_AGENTS'});

    expect(selfTab).toBeInTheDocument();
    expect(otherAgentsTab).toBeInTheDocument();

    expect(selfTab).toHaveAttribute('data-testid', 'mock-tabs-trigger-self');
    expect(otherAgentsTab).toHaveAttribute('data-testid', 'mock-tabs-trigger-other-agents');
  });

  test('TabsList has correct classes', () => {
    render(<InboxSidebarHeader />);
    const tabsList = screen.getByTestId('mock-tabs-list');
    expect(tabsList).toHaveClass(
      'grid grid-cols-2 w-full h-[46px] bg-transparent border-b border-border rounded-none p-0',
    );
  });

  test('TabsTrigger components have correct classes', () => {
    render(<InboxSidebarHeader />);
    const selfTab = screen.getByRole('button', {name: 'INBOX_TAB_SELF'});
    const otherAgentsTab = screen.getByRole('button', {name: 'INBOX_TAB_OTHER_AGENTS'});

    expect(selfTab).toHaveClass('tab-trigger-base font-sans p-0');
    expect(otherAgentsTab).toHaveClass('tab-trigger-base font-sans p-0');
  });
});
