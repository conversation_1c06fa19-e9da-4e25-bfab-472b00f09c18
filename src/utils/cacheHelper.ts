import CryptoJS from 'crypto-js';

// Secret key for encryption - in a real app, this should be stored in environment variables
const SECRET_KEY: string = (import.meta.env.VITE_CACHE_SECRET_KEY as string) ?? 'kshdlkshdlkyerlihbrejkh4ygkq3uy4gk';

/**
 * Encrypts data before storing it
 * @param data The data to encrypt
 * @returns The encrypted data as string
 */
const encryptData = (data: string): string => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

/**
 * Decrypts stored data
 * @param encryptedData The encrypted data
 * @returns The decrypted data or null if decryption fails
 */
const decryptData = (encryptedData: string | null): string | null => {
  if (!encryptedData) return null;

  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    return decryptedData || null;
  } catch (error) {
    console.error('Failed to decrypt data:', error);
    return null;
  }
};

/**
 * Stores a string value securely in localStorage with encryption
 * @param key The key to store the value under
 * @param value The string value to store
 */
export const setSecureItem = (key: string, value: string): void => {
  if (!key || !value) return;
  
  try {
    const encryptedValue = encryptData(value);
    localStorage.setItem(key, encryptedValue);
  } catch (error) {
    console.error(`Failed to store secure item with key "${key}":`, error);
  }
};

/**
 * Retrieves and decrypts a string value from localStorage
 * @param key The key to retrieve the value for
 * @returns The decrypted string value or null if not found or decryption fails
 */
export const getSecureItem = (key: string): string | null => {
  if (!key) return null;
  
  try {
    const encryptedValue = localStorage.getItem(key);
    return decryptData(encryptedValue);
  } catch (error) {
    console.error(`Failed to retrieve secure item with key "${key}":`, error);
    return null;
  }
};

/**
 * Stores an object securely in localStorage with encryption
 * @param key The key to store the object under
 * @param obj The object to store
 */
export const setSecureObject = <T>(key: string, obj: T): void => {
  if (!key || obj === null || obj === undefined) return;
  
  try {
    const jsonString = JSON.stringify(obj);
    setSecureItem(key, jsonString);
  } catch (error) {
    console.error(`Failed to store secure object with key "${key}":`, error);
  }
};

/**
 * Retrieves and decrypts an object from localStorage
 * @param key The key to retrieve the object for
 * @returns The decrypted object or null if not found, decryption fails, or parsing fails
 */
export const getSecureObject = <T>(key: string): T | null => {
  if (!key) return null;
  
  try {
    const jsonString = getSecureItem(key);
    if (!jsonString) return null;
    
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error(`Failed to retrieve secure object with key "${key}":`, error);
    return null;
  }
};

/**
 * Stores a plain (unencrypted) string value in localStorage
 * @param key The key to store the value under
 * @param value The string value to store
 */
export const setItem = (key: string, value: string): void => {
  if (!key || !value) return;
  
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.error(`Failed to store item with key "${key}":`, error);
  }
};

/**
 * Retrieves a plain (unencrypted) string value from localStorage
 * @param key The key to retrieve the value for
 * @returns The string value or null if not found
 */
export const getItem = (key: string): string | null => {
  if (!key) return null;
  
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.error(`Failed to retrieve item with key "${key}":`, error);
    return null;
  }
};

/**
 * Stores a plain (unencrypted) object in localStorage
 * @param key The key to store the object under
 * @param obj The object to store
 */
export const setObject = <T>(key: string, obj: T): void => {
  if (!key || obj === null || obj === undefined) return;
  
  try {
    const jsonString = JSON.stringify(obj);
    localStorage.setItem(key, jsonString);
  } catch (error) {
    console.error(`Failed to store object with key "${key}":`, error);
  }
};

/**
 * Retrieves a plain (unencrypted) object from localStorage
 * @param key The key to retrieve the object for
 * @returns The object or null if not found or parsing fails
 */
export const getObject = <T>(key: string): T | null => {
  if (!key) return null;
  
  try {
    const jsonString = localStorage.getItem(key);
    if (!jsonString) return null;
    
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error(`Failed to retrieve object with key "${key}":`, error);
    return null;
  }
};

/**
 * Removes an item from localStorage
 * @param key The key of the item to remove
 */
export const removeItem = (key: string): void => {
  if (!key) return;
  
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Failed to remove item with key "${key}":`, error);
  }
};

/**
 * Removes multiple items from localStorage
 * @param keys Array of keys to remove
 */
export const removeItems = (keys: string[]): void => {
  if (!keys || keys.length === 0) return;
  
  keys.forEach(key => removeItem(key));
};

/**
 * Clears all items from localStorage
 */
export const clearAll = (): void => {
  try {
    localStorage.clear();
  } catch (error) {
    console.error('Failed to clear localStorage:', error);
  }
};

/**
 * Checks if a key exists in localStorage
 * @param key The key to check
 * @returns True if the key exists, false otherwise
 */
export const hasItem = (key: string): boolean => {
  if (!key) return false;
  
  try {
    return localStorage.getItem(key) !== null;
  } catch (error) {
    console.error(`Failed to check if item exists with key "${key}":`, error);
    return false;
  }
};

/**
 * Gets all keys from localStorage
 * @returns Array of all keys in localStorage
 */
export const getAllKeys = (): string[] => {
  try {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) keys.push(key);
    }
    return keys;
  } catch (error) {
    console.error('Failed to get all keys from localStorage:', error);
    return [];
  }
};
