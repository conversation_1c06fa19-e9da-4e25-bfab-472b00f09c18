@import 'tailwindcss';
@import 'tw-animate-css';

/* Tab triggers */
.tab-trigger-base {
  @apply relative text-left text-sm leading-[21px] tracking-normal font-sans rounded-none font-normal;
  color: var(--color-tab-text-default);
  min-width: 6rem;
  max-width: 11rem;
}

.tab-trigger-base[data-state='active'] {
  @apply font-medium;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: var(--color-tab-text-active);
}

.tab-trigger-base::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 68px;
  border-bottom: 2px solid transparent;
}

.tab-trigger-base[data-state='active']::after {
  border-bottom-color: var(--color-tab-active-border);
}

/* Inbox tab triggers */
.inbox-tab-trigger {
  @apply w-[106px] h-full flex items-center justify-center gap-2 text-sm transition-colors bg-transparent shadow-none;
}

.inbox-tab-trigger[data-state='active'] {
  background-color: var(--color-inbox-tab-bg-active);
  border: 1px solid var(--color-inbox-tab-border-active);
  border-bottom-color: transparent;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

/* Tab text inside inbox-tab-trigger */
.inbox-tab-trigger > span {
  @apply font-normal text-[var(--color-tab-text-active)] leading-[21px] tracking-normal font-sans;
}

.inbox-tab-trigger[data-state='active'] > span {
  @apply font-medium;
}

/* Chat list */
.chat-item:hover {
  background-color: var(--color-chat-item-hover-bg);
}

.chat-item {
  @apply w-full h-[65px] cursor-pointer flex items-center gap-3 px-3 py-3 border-b border-[var(--border-gray-color)] transition-colors duration-300 ease-in-out box-border;
}

.chat-item.selected {
  background-color: var(--color-chat-item-selected-bg);
  opacity: 1;
  border-left-width: 2px;
  border-left-style: solid;
  border-left-color: var(--color-chat-item-selected-border);
}

/* Badge for chat list count */
.badge-chat-list-count {
  @apply bg-[var(--color-badge-bg)] text-white rounded-[8px] min-w-[24px] h-[17px] px-1 text-[10px] leading-none flex items-center justify-center overflow-hidden whitespace-nowrap;
}

/* Button destructive */
.btn-destructive-custom {
  @apply rounded-[5px];
  width: 88px;
  height: 40px;
  background-color: var(--color-btn-destructive-bg);
  opacity: 1;
  top: 213px;
  left: 1119px;
}

/* Input message container and textarea */
.input-message-container {
  @apply relative p-4 max-w-[900px];
}

.input-message-box {
  @apply bg-white border rounded-[10px] resize-none w-full border-[var(--color-border-input)];
  padding: 4px;
  box-sizing: border-box;
}

.input-message-box::placeholder {
  margin-top: 20px;
}

/* User icons row */
.user-icons-row {
  @apply absolute bottom-8 right-16 flex items-center gap-1;
}

/* Vertical dividers */
.vertical-divider {
  @apply h-4 w-px bg-gray-300 mx-1;
}

.vertical-divider-header {
  @apply h-8 w-px bg-gray-300 mx-1;
}

/* Send icon wrapper */
.send-icon-wrapper {
  @apply absolute bottom-8 right-6 w-[30px] h-[30px] bg-[var(--button-blue)] rounded-[5px] flex items-center justify-center cursor-pointer;
}

.send-msg-icon {
  @apply w-6 h-6 object-contain;
}

/* Chat message tails */
.left-message-tail {
  @apply relative;
}

.right-message-tail {
  @apply relative;
}
.right-message-tail::after {
  content: '';
  @apply absolute bottom-0 right-[-8px] w-0 h-0 z-[1];
  border-top: 10px solid var(--chat-bg-accent);
  border-left: 8px solid transparent;
}
.gen-ai-editor-wrapper {
  background: linear-gradient(180deg, rgba(151, 81, 255, 0.1) 0%, rgba(180, 53, 112, 0.1) 100%);
  background-repeat: no-repeat;
  background-position: 0% 0%;
  background-clip: padding-box;
  border: 1px solid var(--color-border-input);
  border-radius: 10px;
}
.gen-ai-tools-wrapper {
  background: linear-gradient(180deg, rgba(151, 81, 255, 0.15) 0%, rgba(180, 53, 112, 0.15) 100%);
  border-radius: 8px;
}
