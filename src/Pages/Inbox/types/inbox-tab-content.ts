import type {InboxTabsEnum} from '@/enums';

export type IInboxChatContent = {
  tabId: number;
  tabName: InboxTabsEnum;
  chatList: IInboxChatList[];
};

export type IInboxChatList = {
  channelId: number;
  recepientId: number;
  recepientName: string;
  recepientProfilrUrl: string;
  latestMessageInfo: {
    message: string;
    timestamp: string;
    unreadMessageCount: number;
    requestLabel: string;
  };
};
