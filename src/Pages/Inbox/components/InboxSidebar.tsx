import {Tabs} from '@/components/ui/tabs';

import type {InboxSidebarContentProps} from '../types/inbox-component-props';
import {InboxChatList, InboxSearch, InboxSidebarHeader} from '.';
import {useCallback, useEffect, useState} from 'react';
import type {IInboxChatList} from '../types';

export const InboxSidebar: React.FC<InboxSidebarContentProps> = ({sideBarContent}) => {
  const [displayedChatList, setDisplayedChatList] = useState<IInboxChatList[]>(sideBarContent.chatList);

  useEffect(() => {
    setDisplayedChatList(sideBarContent.chatList);
  }, [sideBarContent.chatList]);

  const handleSearchResults = useCallback((results: IInboxChatList[] | null) => {
    setDisplayedChatList(results ?? []);
  }, []);

  return (
    <div
      className="w-65 min-w-60 flex-shrink-0 border-r border-border flex flex-col h-full"
      data-testid="inbox-sidebar"
    >
      <Tabs defaultValue="self" className="flex flex-col flex-1 min-h-0 gap-0">
        {/* Sidebar Tabs Header */}
        <InboxSidebarHeader />

        {/* Search Input */}
        <InboxSearch onSearchResults={handleSearchResults} activeTab={sideBarContent.tabName} />
        {/* Scrollable Tab Chat List */}
        <InboxChatList chatListContent={{...sideBarContent, chatList: displayedChatList}} />
      </Tabs>
    </div>
  );
};
