import {lazy} from 'react';
import {RouteConstant} from '../../constants';
const TeamsPage = lazy(() => import('@/Pages/Teams/TeamsPage'));
const HomePage = lazy(() => import('@/Pages/Home/Home'));
const AddAgentPage = lazy(() => import('@/Pages/Agents/AddAgentPage'));
const InboxPage = lazy(() => import('@/Pages/Inbox/Inbox'));
const SettingsPage = lazy(() => import('@/Pages/Settings/Settings'));
const BusinessHoursPage = lazy(() => import('@/Pages/Settings/ChildrenPages/BusinessHours'));
const CustomHoursPage = lazy(() => import('@/Pages/Settings/ChildrenPages/CustomBusinessHoursPanel'));
const RoutingRulesPage = lazy(() => import('@/Pages/Settings/ChildrenPages/RoutingRules'));

const mainLayoutRouteConfig = [
  {
    path: RouteConstant.HOME,
    component: HomePage,
  },
  {
    path: RouteConstant.TEAMS,
    component: TeamsPage,
  },
  {
    path: RouteConstant.ADD_AGENT,
    component: AddAgentPage,
  },
  {
    path: RouteConstant.INBOX,
    component: InboxPage,
  },
  {
    path: RouteConstant.SETTINGS,
    component: SettingsPage,
  },
  {
    path: RouteConstant.BUSINESS_HOURS,
    component: BusinessHoursPage,
  },
  {
    path: RouteConstant.ADD_BUSINESS_HOURS,
    component: CustomHoursPage,
  },
  {
    path: RouteConstant.EDIT_BUSINESS_HOURS,
    component: CustomHoursPage,
  },
  {
    path: RouteConstant.SETTINGS_ROUTING_RULES,
    component: RoutingRulesPage,
  },
];

export default mainLayoutRouteConfig;
