import {describe, it, expect} from 'vitest';
import {data} from './data';
import type {ThemeVariables} from '@/types/theme.type';

describe('theme data', () => {
  it('should export theme variables with correct structure', () => {
    expect(data).toBeDefined();
    expect(data).toHaveProperty('primary');
    expect(data).toHaveProperty('foreground');
    expect(data).toHaveProperty('background');
    expect(data).toHaveProperty('card');
    expect(data).toHaveProperty('ring');
    expect(data).toHaveProperty('border');
  });

  it('should have valid OKLCH color values', () => {
    // Check that all values follow OKLCH format
    const oklchPattern = /^oklch\(\d+(\.\d+)?\s+\d+(\.\d+)?\s+\d+(\.\d+)?\)$/;

    Object.values(data).forEach(value => {
      expect(value).toMatch(oklchPattern);
    });
  });

  it('should match the expected theme values', () => {
    const expectedTheme: ThemeVariables = {
      primary: 'oklch(0.618 0.201 263)',
      foreground: 'oklch(0.382 0 0)',
      background: 'oklch(1 0 0)',
      card: 'oklch(1 0 0)',
      ring: 'oklch(0.708 0 0)',
      border: 'oklch(0.782 0 0)',
    };

    expect(data).toEqual(expectedTheme);
  });
});
