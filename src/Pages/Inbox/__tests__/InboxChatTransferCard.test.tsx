import {render, screen, fireEvent} from '@testing-library/react';

import {describe, it, expect, vi} from 'vitest';
import InboxChatTransferCard from '../components/InboxChatTransferCard';

describe('InboxChatTransferCard', () => {
  const props = {
    imageSrc: 'test-image.jpg',
    heading: 'Test Heading',
    subtitle: 'Test Subtitle',
    buttons: [
      {label: 'Accept', onClick: vi.fn(), variant: 'default' as any},
      {label: 'Transfer', onClick: vi.fn(), variant: 'default' as any},
    ],
    footerContent: <div data-testid="footer">Test Footer</div>,
  };

  it('renders correctly', () => {
    render(<InboxChatTransferCard {...props} />);

    expect(screen.getByAltText('Banner')).toBeInTheDocument();
    expect(screen.getByText('Test Heading')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
    expect(screen.getByText('Test Footer')).toBeInTheDocument();
    expect(screen.getByText('Accept')).toBeInTheDocument();
    expect(screen.getByText('Transfer')).toBeInTheDocument();
  });

  it('calls onClick when buttons are clicked', () => {
    render(<InboxChatTransferCard {...props} />);
    fireEvent.click(screen.getByText('Accept'));
    expect(props.buttons[0].onClick).toHaveBeenCalled();

    fireEvent.click(screen.getByText('Transfer'));
    expect(props.buttons[1].onClick).toHaveBeenCalled();
  });
});
