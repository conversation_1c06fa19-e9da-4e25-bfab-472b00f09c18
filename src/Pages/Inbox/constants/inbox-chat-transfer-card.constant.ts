import {InboxChatTransferStatus} from '@/enums';
import ChatTransferIcon from '@/assets/inboxPanel/ChatTransferIcon.svg';
import ChatHandlingIcon from '@/assets/inboxPanel/ChatHandlingIcon.svg';
import ChatTransferTimeoutIcon from '@/assets/inboxPanel/ChatTransferTimeoutIcon.svg';
import {t} from 'i18next';
import {ChatTransferLbl} from '@/constants';
import type {InboxChatTransferContent} from '../components';

export const TransferCardConfig: Record<InboxChatTransferStatus, InboxChatTransferContent> = {
  [InboxChatTransferStatus.TRANSFERRING]: {
    imageSrc: ChatTransferIcon,
    heading: t(ChatTransferLbl.CHAT_REQUEST_TRANSFER_HEADING),
    subtitle: t(ChatTransferLbl.CHAT_REQUEST_TRANSFER_SUBTITLE),
  },
  [InboxChatTransferStatus.HANDLING]: {
    imageSrc: ChatHandlingIcon,
    heading: t(ChatTransferLbl.CHAT_REQUEST_RECEIVED_HEADING),
    subtitle: t(ChatTransferLbl.CHAT_REQUEST_RECEIVED_SUBTITLE),
  },
  [InboxChatTransferStatus.TIMEOUT]: {
    imageSrc: ChatTransferTimeoutIcon,
    heading: t(ChatTransferLbl.CHAT_REQUEST_TIMEOUT_HEADING),
    subtitle: t(ChatTransferLbl.CHAT_REQUEST_TIMEOUT_SUBTITLE),
  },
};
