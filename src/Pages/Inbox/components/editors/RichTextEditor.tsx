import {Input} from '@/components/ui/input';
import {MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';
import {useState} from 'react';

export const RichTextEditor = () => {
  const {t} = useTranslation();
  const [inputValue, setInputValue] = useState<string>('');
  return (
    <div
      data-testid="rich-text-editor"
      className="border border-[var(--border-gray-color)] rounded-[10px] px-4 pt-4 pb-2 h-[160px]"
    >
      <div className="flex flex-col justify-between h-full">
        <div>
          <Input
            value={inputValue}
            placeholder={t(MESSAGES.CHAT_INPUT_PLACEHOLDER)}
            className="border-0 focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none shadow-none"
            onChange={e => setInputValue(e.target.value)}
          />
        </div>
      </div>
    </div>
  );
};
