import {render, screen, fireEvent} from '@testing-library/react';
import {BrowserRouter, useLocation} from 'react-router-dom';
import CustomBusinessHoursPanel from '../ChildrenPages/CustomBusinessHoursPanel';
import {vi, type Mock} from 'vitest';
import {describe, test, expect, beforeEach} from 'vitest';

// Mock the useLocation hook
vi.mock('react-router-dom', async importOriginal => {
  const actual = await (importOriginal as any)();
  return {
    ...actual,
    useLocation: vi.fn(),
  };
});

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Mock t function to return the key itself
    i18n: {
      changeLanguage: vi.fn(),
    },
  }),
}));

describe('CustomBusinessHoursPanel Component', () => {
  beforeEach(() => {
    // Reset mock before each test
    (useLocation as Mock).mockReturnValue({pathname: '/settings/business-hours/add'});
  });

  test('renders Add Custom Hours title in add mode', () => {
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    expect(screen.getByText('ADD_CUSTOM_HOURS')).toBeInTheDocument();
  });

  test('renders Edit Custom Hours title in edit mode', () => {
    (useLocation as Mock).mockReturnValue({pathname: '/settings/business-hours/edit/123'});
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    expect(screen.getByText('EDIT_CUSTOM_HOURS')).toBeInTheDocument();
  });

  test('renders "Same Everyday" radio button and selects it by default', () => {
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    const sameEverydayRadio = screen.getByLabelText('SAME_EVERYDAY') as HTMLInputElement;
    expect(sameEverydayRadio).toBeInTheDocument();
    expect(sameEverydayRadio.checked).toBe(true);
  });

  test('renders "Custom" radio button', () => {
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    expect(screen.getByLabelText('CUSTOM')).toBeInTheDocument();
  });

  test('switches to "Custom" schedule type when Custom radio button is clicked', () => {
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    const customRadio = screen.getByLabelText('CUSTOM');
    fireEvent.click(customRadio);
    const sameEverydayRadio = screen.getByLabelText('SAME_EVERYDAY') as HTMLInputElement;
    expect(sameEverydayRadio.checked).toBe(false);
    expect((customRadio as HTMLInputElement).checked).toBe(true);
  });

  test('renders Add Message section in edit mode', () => {
    (useLocation as Mock).mockReturnValue({pathname: '/settings/business-hours/edit/123'});
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    expect(screen.getByText('ADD_MESSAGE')).toBeInTheDocument();
    expect(screen.getByText('ADD_MESSAGE_DESCRIPTION')).toBeInTheDocument();
    expect(
      screen.getByDisplayValue('Our agents are unavailable at the moment. Please reach us during our working hours'),
    ).toBeInTheDocument();
  });

  test('renders Department select in add mode', () => {
    (useLocation as Mock).mockReturnValue({pathname: '/settings/business-hours/add'});
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    expect(screen.getByText('DEPARTMENT')).toBeInTheDocument();
    expect(screen.getByText('TIME_SLOTS_ASSIGNED_TO_DEPARTMENTS')).toBeInTheDocument();
    expect(screen.getByText('Select Departments')).toBeInTheDocument();
  });

  test('renders Cancel and Add buttons', () => {
    render(
      <BrowserRouter>
        <CustomBusinessHoursPanel />
      </BrowserRouter>,
    );
    expect(screen.getByRole('button', {name: 'CANCEL'})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'ADD'})).toBeInTheDocument();
  });
});
