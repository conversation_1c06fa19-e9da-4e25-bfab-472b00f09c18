module.exports = {
  types: [
    {value: 'feat', name: 'feat:     A new feature'},
    {value: 'fix', name: 'fix:      A bug fix'},
    {value: 'docs', name: 'docs:     Documentation only changes'},
    {
      value: 'style',
      name: 'style:    Changes that do not affect the meaning of the code\n            (white-space, formatting, missing semi-colons, etc)',
    },
    {
      value: 'refactor',
      name: 'refactor: A code change that neither fixes a bug nor adds a feature',
    },
    {
      value: 'perf',
      name: 'perf:     A code change that improves performance',
    },
    {value: 'test', name: 'test:     Adding missing tests'},
    {
      value: 'chore',
      name: 'chore:    Changes to the build process or auxiliary tools\n            and libraries such as documentation generation',
    },
    {value: 'revert', name: 'revert:   Revert to a commit'},
    {value: 'wip', name: 'wip:      Work in progress'},
  ],

  scopes: [{name: 'core'}, {name: 'agent'}, {name: 'department'}, {name: 'chat'}],

  // These settings are related to a different, less flexible built-in ticket feature
  // They are kept false as we handle CPAAS- ticket numbers via the 'footer' message.
  appendBranchNameToCommitMessage: false,
  allowTicketNumber: false,
  isTicketNumberRequired: false,
  ticketNumberPrefix: 'CPAAS-',

  // Messages to guide the user during the commit process
  messages: {
    type: "Select the type of change that you're committing:",
    scope: 'Denote the SCOPE of this change:',
    customScope: 'Denote the SCOPE of this change:',
    subject: 'Write a SHORT, IMPERATIVE tense description of the change:\n',
    body: 'Provide a LONGER description of the change (optional). Use "|" to break new line:\n',
    breaking: 'List any BREAKING CHANGES (optional):\n',
    // The footer message guides users to include CPAAS- ticket numbers.
    footer: 'List any ISSUES CLOSED by this change (optional). E.g.: CPAAS-31, CPAAS-34:\n',
    confirmCommit: 'Are you sure you want to proceed with the commit above?',
  },

  allowCustomScopes: false,
  allowBreakingChanges: ['feat', 'fix'],

  // Limit the subject length
  subjectLimit: 100,
  breaklineChar: '|',
  footerPrefix: '',
  askForBreakingChangeFirst: true,
};
