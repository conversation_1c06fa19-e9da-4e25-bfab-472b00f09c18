import {useCallback} from 'react';
import {useAppDispatch, useAppSelector} from '../redux-hooks';
import {
  setCredentials,
  unsetCredentials,
  setPermissions,
  selectCurrentLoginStatus,
  selectCurrentAccessToken,
  selectCurrentRefreshToken,
  selectCurrentAuthState,
  selectCurrentPermissions,
  type AuthResData,
} from '../../redux/auth/authSlice';
import {isTokenExpired} from '../../utils/tokenHelper';
import type {PermissionsEnum} from '../../enums';

export interface UseAuthReturn {
  // State
  isAuthenticated: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  permissions: PermissionsEnum[] | null;
  authState: ReturnType<typeof selectCurrentAuthState>;

  // Actions
  login: (authData: AuthResData) => void;
  logout: () => void;
  setUserPermissions: (permissions: PermissionsEnum[]) => void;
  checkTokenExpiration: () => boolean;

  // Utilities
  hasPermission: (requiredPermissions?: PermissionsEnum[]) => boolean;
}

/**
 * Custom hook for authentication management
 * Provides login, logout, token management, and permission checking functionality
 */
export const useAuth = (): UseAuthReturn => {
  const dispatch = useAppDispatch();

  // Selectors
  const isAuthenticated = useAppSelector(selectCurrentLoginStatus);
  const accessToken = useAppSelector(selectCurrentAccessToken);
  const refreshToken = useAppSelector(selectCurrentRefreshToken);
  const permissions = useAppSelector(selectCurrentPermissions);
  const authState = useAppSelector(selectCurrentAuthState);

  /**
   * Login user with authentication data
   */
  const login = useCallback(
    (authData: AuthResData) => {
      dispatch(setCredentials(authData));
    },
    [dispatch],
  );

  /**
   * Logout user and clear all authentication data
   */
  const logout = useCallback(() => {
    dispatch(unsetCredentials());
  }, [dispatch]);

  /**
   * Set user permissions
   */
  const setUserPermissions = useCallback(
    (userPermissions: PermissionsEnum[]) => {
      dispatch(setPermissions(userPermissions));
    },
    [dispatch],
  );

  /**
   * Check if current token is expired
   */
  const checkTokenExpiration = useCallback((): boolean => {
    const expired = isTokenExpired();
    if (expired && isAuthenticated) {
      // Auto-logout if token is expired
      logout();
    }
    return expired;
  }, [isAuthenticated, logout]);

  /**
   * Check if user has required permissions
   */
  const hasPermission = useCallback(
    (requiredPermissions?: PermissionsEnum[]): boolean => {
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
      }

      if (!permissions || permissions.length === 0) {
        return false;
      }

      return requiredPermissions.every(permission => permissions.includes(permission));
    },
    [permissions],
  );

  return {
    // State
    isAuthenticated,
    accessToken,
    refreshToken,
    permissions,
    authState,

    // Actions
    login,
    logout,
    setUserPermissions,
    checkTokenExpiration,

    // Utilities
    hasPermission,
  };
};
