import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {DepartmentForm} from '../DepartmentForm';

describe('DepartmentForm', () => {
  const mockOnSubmit = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form when isOpen is true', () => {
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    expect(screen.getByTestId('department-form-title')).toBeInTheDocument();
    expect(screen.getByTestId('Name')).toBeInTheDocument();
    expect(screen.getByText('CANCEL_ADDING')).toBeInTheDocument();
    expect(screen.getByText('ADD')).toBeInTheDocument();
  });

  it('does not render the form when isOpen is false', () => {
    render(<DepartmentForm isOpen={false} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    expect(screen.queryByTestId('department-form-title')).not.toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup();
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    const cancelButton = screen.getByText('CANCEL_ADDING');
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows validation error for empty name', async () => {
    const user = userEvent.setup();
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    // Submit the form without entering a name
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check if validation error is shown
    await waitFor(() => {
      expect(screen.getByText('Department name is required')).toBeInTheDocument();
    });

    // Check that onSubmit was not called
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('shows validation error for invalid characters', async () => {
    const user = userEvent.setup();
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    // Enter a name with special characters
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'HR Department!@#');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check if validation error is shown
    await waitFor(() => {
      expect(screen.getByText('Only alphanumeric characters are allowed')).toBeInTheDocument();
    });

    // Check that onSubmit was not called
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('submits the form with valid data', async () => {
    const user = userEvent.setup();
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    // Enter a valid name
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, 'HR Department');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check that onSubmit was called with the correct data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({name: 'HR Department'});
    });
  });

  it('trims whitespace from the name', async () => {
    const user = userEvent.setup();
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    // Enter a name with leading and trailing whitespace
    const nameInput = screen.getByTestId('Name');
    await user.type(nameInput, '  HR Department  ');

    // Submit the form
    const saveButton = screen.getByText('ADD');
    await user.click(saveButton);

    // Check that onSubmit was called with the trimmed name
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({name: 'HR Department'});
    });
  });

  it('shows loading state when isLoading is true', () => {
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} isLoading={true} />);

    // Check if the save button shows loading state
    expect(screen.getByText('ADDING...')).toBeInTheDocument();
    expect(screen.getByText('ADDING...')).toBeDisabled();
  });

  it('resets the form when closed', async () => {
    const user = userEvent.setup();
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    // Enter a name
    const nameInput = screen.getByTestId('Name');

    await user.type(nameInput, 'HR Department');

    // Close the form
    const cancelButton = screen.getByText('CANCEL_ADDING');
    await user.click(cancelButton);

    // Reopen the form
    render(<DepartmentForm isOpen={true} onClose={mockOnClose} onSubmit={mockOnSubmit} />);

    // Check that the input is empty
    expect(nameInput).toHaveValue('');
  });
});
