/**
 * Environment configuration with type safety
 */

// Define the structure of our environment variables
export interface EnvConfig {
  AGENT_PORTAL_FACADE_API_BASE_URL: string;
}

// Get environment variables with fallbacks
export const env: EnvConfig = {
  AGENT_PORTAL_FACADE_API_BASE_URL:
    import.meta.env.VITE_AGENT_PORTAL_FACADE_API_BASE_URL || 'https://chatbot.ngagecpaas.com/api/v1',
};

// Validate required environment variables
export function validateEnv(): void {
  const missingVars: string[] = [];

  if (!env.AGENT_PORTAL_FACADE_API_BASE_URL) {
    missingVars.push('VITE_AGENT_PORTAL_FACADE_API_BASE_URL');
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}. Please check your .env file.`);
  }
}
