import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>rk<PERSON>, Send} from 'lucide-react';
import {InboxChatContentHeader, InboxChatSideProfile, InboxPreviousConversation, InboxMessages} from '.';
import InboxChatTransferCard, {type ButtonProps} from './InboxChatTransferCard';
import {InboxChatSuggestion} from './InboxChatSuggestion';
import {useMemo, useRef, useState, useEffect} from 'react';
import type {IInboxChatContent, Message} from '../types';
import {ChatInputMode} from '../types';
import {InboxChatTransferStatus, InboxTabsEnum} from '@/enums';
import InboxChatEmojiModal from './InboxChatEmojiModal';
import type {EmojiClickData} from 'emoji-picker-react';
import {Button} from '@/components/ui';
import {messagesData} from '../mockData/mock-chat-content';
import {DefaultEditor} from './editors/DefaultEditor';
import {RichTextEditor} from './editors/RichTextEditor';
import {GenAIEditor} from './editors/GenAIEditor';
import {cn} from '@/lib/utils';
import {TransferCardConfig} from '../constants';
import {ChatTransferLbl} from '@/constants';
import {useTranslation} from 'react-i18next';
import ChatFileUploader from './ChatFileUploader';

const suggestions = ['Hello! How can I assist you today?', 'Hello there!'];
type InboxChatContentProps = {
  chatContent: IInboxChatContent;
};
export const InboxChatContent: React.FC<InboxChatContentProps> = ({chatContent}) => {
  const toggleButtonRef = useRef<HTMLButtonElement | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [messages, setMessages] = useState<Message[]>(messagesData);

  const {t} = useTranslation();
  const [inputMode, setInputMode] = useState<ChatInputMode>(ChatInputMode.DEFAULT);

  const modeComponentMap: Record<ChatInputMode, React.ReactNode> = useMemo(
    () => ({
      [ChatInputMode.DEFAULT]: <DefaultEditor inputValue={inputValue} setInputValue={setInputValue} />,
      [ChatInputMode.RICH_TEXT]: <RichTextEditor />,
      [ChatInputMode.GEN_AI]: <GenAIEditor />,
    }),
    [inputValue],
  );

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleSend = (msg?: string) => {
    const message = msg ?? inputValue;
    if (!message.trim() && !selectedFile) return;

    const newMessage: Message = {
      id: messages.length + 1,
      text: message,
      sender: 'right',
      time: new Date().toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'}),
    };

    if (selectedFile) {
      newMessage.file = selectedFile;
      newMessage.fileType = selectedFile?.type;
      newMessage.fileName = selectedFile?.name;
    }

    setMessages([...messages, newMessage]);
    setInputValue('');
    setSelectedFile(null); // Clear file after sending
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSend(suggestion);
    setShowSuggestions(false);
  };

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    // add your logic to use the emoji
    setInputValue(prev => prev + emojiData.emoji);
  };

  const toggleTextMode = (mode: ChatInputMode) => {
    setInputMode(inputMode !== mode ? mode : ChatInputMode.DEFAULT);
  };

  const [showTransferCard, setShowTransferCard] = useState(true);
  const [transferCardStatus, setTransferCardStatus] = useState<InboxChatTransferStatus>(
    InboxChatTransferStatus.TRANSFERRING,
  );

  const transferTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleChatTransfer = () => {
    setShowTransferCard(true);
    setTransferCardStatus(InboxChatTransferStatus.HANDLING);
    // Clear any previous timeout
    if (transferTimeoutRef.current) {
      clearTimeout(transferTimeoutRef.current);
    }
    // Set new timeout
    transferTimeoutRef.current = setTimeout(() => {
      setTransferCardStatus(InboxChatTransferStatus.TIMEOUT);
    }, 2500);
  };

  const cancelTransfer = () => {
    if (transferTimeoutRef.current) {
      clearTimeout(transferTimeoutRef.current);
      transferTimeoutRef.current = null;
    }
    setShowTransferCard(false);
  };

  // Auto-clear timeout on unmount
  useEffect(() => {
    return () => {
      if (transferTimeoutRef.current) {
        clearTimeout(transferTimeoutRef.current);
      }
    };
  }, []);

  const handleTransferButtons: () => ButtonProps[] = () => {
    // this will show buttons based on the transfer card status
    switch (transferCardStatus) {
      case InboxChatTransferStatus.TRANSFERRING:
        return [
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_TRANSFER_CANCEL_BUTTON),
            onClick: cancelTransfer,
            className: 'text-primary',
            variant: 'outline',
          },
        ];
      case InboxChatTransferStatus.HANDLING:
        return [
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_RECEIVED_DECLINE_BUTTON),
            onClick: cancelTransfer,
            className: 'text-primary',
            variant: 'outline',
          },
          {
            label: t(t(ChatTransferLbl.CHAT_REQUEST_RECEIVED_ACCEPT_BUTTON)),
            onClick: cancelTransfer,
            variant: 'primary',
          },
        ];
      case InboxChatTransferStatus.TIMEOUT:
        return [
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_TIMEOUT_CONTINUE_BUTTON),
            onClick: cancelTransfer,
            variant: 'primary',
          },
          {
            label: t(ChatTransferLbl.CHAT_REQUEST_TIMEOUT_RETRY_BUTTON),
            onClick: handleChatTransfer,
            className: 'text-primary',
            variant: 'outline',
          },
        ];
      default:
        return [];
    }
  };
  return (
    <div className="flex-1 flex flex-row overflow-hidden flex-grow">
      {/* Center chat viewer (main area) */}
      <div className="flex-1 flex flex-col overflow-y-auto">
        {/**Chat Header */}
        <InboxChatContentHeader handleTransferCard={handleChatTransfer} />

        {/**Chat Messages */}
        <div className="flex-1 p-5 overflow-auto">
          {/* Previous Agent Chat Messages */}
          {/***hardcoded tab for now for purpose of demo, will be removed later */}
          {chatContent.tabName === InboxTabsEnum.QUEUED && <InboxPreviousConversation />}
          <InboxMessages messages={messages} />
        </div>
        {/* Transfer Banner */}
        {/***hardcoded tab for now for purpose of demo, will be removed later */}
        {showTransferCard && chatContent.tabName !== InboxTabsEnum.QUEUED && (
          <div className="py-4 px-6">
            <InboxChatTransferCard
              {...TransferCardConfig[transferCardStatus]}
              buttons={handleTransferButtons()}
              footerContent={''}
            />
          </div>
        )}
        {/**Chat Suggestions */}
        {showSuggestions && (
          <div className="flex gap-2 px-5 pb-2 justify-end">
            {suggestions.map(suggestion => (
              <InboxChatSuggestion key={suggestion} suggestion={suggestion} onSuggestionClick={handleSuggestionClick} />
            ))}
          </div>
        )}

        {/* File preview above input */}
        {selectedFile && (
          <div className="flex items-center gap-2 px-5 pb-2">
            {selectedFile.type.startsWith('image/') ? (
              <img
                src={URL.createObjectURL(selectedFile)}
                alt={selectedFile.name}
                className="h-12 w-12 object-cover rounded border border-gray-400"
              />
            ) : (
              <span className="text-sm font-medium">{selectedFile.name}</span>
            )}
            <button type="button" className="text-red-500 text-xs" onClick={() => setSelectedFile(null)}>
              {t('REMOVE')}
            </button>
          </div>
        )}

        {/**Chat Input */}
        <div className="p-4">
          <div className="relative mb-[18px] transition-all duration-300">
            {modeComponentMap[inputMode]}
            <div className="absolute right-[30px] bottom-0 flex items-center gap-[10px] py-[10px]">
              <Button
                variant={'ghost'}
                onClick={() => toggleTextMode(ChatInputMode.GEN_AI)}
                data-testid="gen-ai-button"
                className={cn(
                  'p-0',
                  '!px-0',
                  'text-[var(--accent-purple)]',
                  inputMode === ChatInputMode.GEN_AI && 'bg-primary/10',
                )}
              >
                <Sparkles size={20} className="text-[var(--sparkle-gradient)]" />
              </Button>
              <Button
                variant={'ghost'}
                onClick={() => toggleTextMode(ChatInputMode.RICH_TEXT)}
                data-testid="rich-text-icon"
                className={cn(
                  'p-0',
                  '!px-0',
                  'text-[var(--text-gray-color)]',
                  inputMode === ChatInputMode.RICH_TEXT && 'bg-primary/10',
                )}
              >
                <TypeOutline size={20} className="text-[var(--text-gray-color)]" />
              </Button>
              <div className="h-4 w-px bg-gray-300 mx-1" />
              <button
                ref={toggleButtonRef}
                type="button"
                onClick={() => {
                  setShowEmojiPicker(!showEmojiPicker);
                }}
                className="bg-transparent border-none p-0 m-0 cursor-pointer"
                aria-label="Open emoji picker"
              >
                <Smile size={20} className="text-[var(--text-gray-color)]" />
              </button>
              {/* Place ChatFileUploader here */}
              <ChatFileUploader onFileSelect={file => setSelectedFile(file)} />
              <Link size={20} className="text-[var(--text-gray-color)]" />
              <div className="h-4 w-px bg-gray-300 mx-1" />
              <button
                data-testid="send-button"
                className="w-[38px] h-[38px] bg-[var(--button-blue)] rounded-[5px] flex items-center justify-center cursor-pointer"
                onClick={() => handleSend()}
                type="button"
              >
                <Send className="w-6 h-6 object-contain text-white" />
              </button>
            </div>
          </div>
        </div>
        {/* Emoji picker */}
        {showEmojiPicker && (
          <InboxChatEmojiModal
            show={showEmojiPicker}
            onClose={() => setShowEmojiPicker(false)}
            onEmojiClick={handleEmojiClick}
            toggleButtonRef={toggleButtonRef}
          />
        )}
      </div>

      {/* Right Side Profile*/}
      <InboxChatSideProfile />
    </div>
  );
};
