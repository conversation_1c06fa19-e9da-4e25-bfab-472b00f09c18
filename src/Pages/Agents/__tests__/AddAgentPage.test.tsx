import {describe, it, expect, vi, beforeEach} from 'vitest';
import {screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as reactRouter from 'react-router-dom';
import AddAgentPage from '@/Pages/Agents/AddAgentPage';
import * as agentSlice from '@/redux/agents/agentSlice';
import {renderWithStore} from '@/testUtils/test-utils';
import * as departmentSlice from '@/redux/departments/departmentSlice';
renderWithStore;

// Mock the dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
  useSearchParams: vi.fn(() => [
    new URLSearchParams(''), // or just '' if no param needed
    vi.fn(), // setSearchParams mock
  ]),
}));

vi.mock('@/redux/agents/agentSlice', () => ({
  useCreateAgentMutation: vi.fn(),
  useUpdateAgentMutation: vi.fn(() => [vi.fn(), {isLoading: false}]),
  useGetAgentByIdQuery: vi.fn((id: string) => ({
    data: {
      id,
      name: 'Mock Agent',
      email: '<EMAIL>',
      mobile: '9999999999',
      departmentName: 'dep-001',
    },
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  })),
}));

const mockCreateDepartment = vi.fn();

vi.mock('@/redux/departments/departmentSlice', () => ({
  useGetDepartmentsQuery: vi.fn(),
  useCreateDepartmentMutation: vi.fn(() => [mockCreateDepartment, {isLoading: false}]),
}));

describe('AddAgentPage', () => {
  const mockNavigate = vi.fn();
  const mockCreateAgent = vi.fn();
  const mockDepartments = {
    data: [
      {id: '1', name: 'HR'},
      {id: '2', name: 'IT'},
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (reactRouter.useNavigate as any).mockReturnValue(mockNavigate);
    (agentSlice.useCreateAgentMutation as any).mockReturnValue([mockCreateAgent, {isLoading: false}]);
    (departmentSlice.useGetDepartmentsQuery as any).mockReturnValue({
      data: mockDepartments,
      isLoading: false,
    });
    Element.prototype.hasPointerCapture = () => false;
    Element.prototype.scrollIntoView = () => {};
  });

  it('renders the agent form', () => {
    renderWithStore(<AddAgentPage />);
    expect(screen.getByText('ADD_AGENT_CRUMB')).toBeInTheDocument();
  });

  it('creates an agent and when form is submitted', async () => {
    const user = userEvent.setup();
    mockCreateAgent.mockResolvedValue({id: '1', name: 'John Doe'});

    renderWithStore(<AddAgentPage />);

    // Fill in the fields like the real user would
    await user.type(screen.getByTestId('Name'), 'John Doe');
    await user.type(screen.getByTestId('Email'), '<EMAIL>');
    await user.type(screen.getByPlaceholderText(''), '9876543210');

    // Open and select department from dropdown
    await user.click(screen.getByTestId('department-trigger'));
    await user.click(screen.getByTestId('department-option-HR')); // exact testId used in AgentForm

    // Click the Add button
    await user.click(screen.getByText('ADD'));

    // Verify agent creation
    await waitFor(() => {
      expect(mockCreateAgent).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        mobile: '+919876543210', // react-tel-input formats it
        departmentId: '1', // department ID, not name
      });
    });
  });

  it('handles error when agent creation fails', async () => {
    mockCreateAgent.mockRejectedValue(new Error('API Error'));
    console.error = vi.fn();
    renderWithStore(<AddAgentPage />);
    // Submit the form
    const submitEvent = new CustomEvent('submit');

    // Simulate form submission
    await waitFor(() => {
      const form = screen.getByTestId('agent-form');
      form.dispatchEvent(submitEvent);
    });

    // Check if error is logged
  });
});
