import {render, screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {DefaultEditor} from '../components/editors/DefaultEditor';
import {MESSAGES} from '@/constants';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('DefaultEditor', () => {
  const mockSetInputValue = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders the DefaultEditor component with textarea', () => {
    render(<DefaultEditor inputValue="" setInputValue={mockSetInputValue} />);

    const textarea = screen.getByPlaceholderText(MESSAGES.CHAT_INPUT_PLACEHOLDER);
    expect(textarea).toBeInTheDocument();
    expect(textarea.tagName.toLowerCase()).toBe('textarea');
  });

  test('displays the provided input value', () => {
    render(<DefaultEditor inputValue="Hello world" setInputValue={mockSetInputValue} />);

    const textarea = screen.getByPlaceholderText(MESSAGES.CHAT_INPUT_PLACEHOLDER);
    expect(textarea).toHaveValue('Hello world');
  });

  test('calls setInputValue when text is entered', () => {
    render(<DefaultEditor inputValue="" setInputValue={mockSetInputValue} />);

    const textarea = screen.getByPlaceholderText(MESSAGES.CHAT_INPUT_PLACEHOLDER);
    fireEvent.change(textarea, {target: {value: 'New message'}});

    expect(mockSetInputValue).toHaveBeenCalledWith('New message');
  });
});
