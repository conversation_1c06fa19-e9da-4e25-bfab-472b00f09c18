import {describe, it, expect, vi} from 'vitest';
import {screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TeamsPage from '../TeamsPage';
import {renderWithProviders} from '@/testUtils/test-utils';

// Mock the imported components
vi.mock('@/Pages/Agents/AgentsPage', () => ({
  default: () => <div data-testid="agents-page">Agents Page Content</div>,
}));

vi.mock('@/Pages/Departments/DepartmentPage', () => ({
  default: () => <div data-testid="department-page">Department Page Content</div>,
}));

describe('TeamsPage', () => {
  it('renders the page title', async () => {
    renderWithProviders(<TeamsPage />);
    expect(screen.getByText('TEAM')).toBeInTheDocument();
  });

  it('renders tabs with correct labels', async () => {
    renderWithProviders(<TeamsPage />);
    expect(screen.getByRole('tab', {name: 'AGENT'})).toBeInTheDocument();
    expect(screen.getByRole('tab', {name: 'DEPARTMENTS'})).toBeInTheDocument();
  });

  it('shows AgentsPage by default', async () => {
    renderWithProviders(<TeamsPage />);
    expect(screen.getByTestId('agents-page')).toBeInTheDocument();
    expect(screen.queryByTestId('department-page')).not.toBeInTheDocument();
  });

  it('switches to DepartmentPage when Departments tab is clicked', async () => {
    const user = userEvent.setup();
    renderWithProviders(<TeamsPage />);

    await user.click(screen.getByRole('tab', {name: 'DEPARTMENTS'}));

    expect(screen.getByTestId('department-page')).toBeInTheDocument();
    expect(screen.queryByTestId('agents-page')).not.toBeInTheDocument();
  });
});
