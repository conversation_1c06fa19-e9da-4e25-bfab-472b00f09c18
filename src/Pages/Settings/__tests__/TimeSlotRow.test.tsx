import {render, screen, fireEvent} from '@testing-library/react';
import {TimeSlotRow} from '../components/TimeSlotRow';

import {vi, describe, test, expect, beforeEach} from 'vitest';

describe('TimeSlotRow Component', () => {
  const mockOnAddRow = vi.fn();
  const mockOnRemoveRow = vi.fn();

  beforeEach(() => {
    mockOnAddRow.mockClear();
    mockOnRemoveRow.mockClear();
  });

  test('renders "from" and "to" time inputs with default values', () => {
    const slot = {from: '10:00 AM', to: '12:00 PM', id: '1'};
    render(
      <TimeSlotRow
        slot={slot}
        slotIndex={0}
        totalSlots={1}
        onAddRow={mockOnAddRow}
        onRemoveRow={mockOnRemoveRow}
        canAdd={true}
        canRemove={true}
      />,
    );

    expect(screen.getByDisplayValue('10:00 AM')).toBeInTheDocument();
    expect(screen.getByDisplayValue('12:00 PM')).toBeInTheDocument();
  });

  test('renders SquarePlus icon when canAdd is true and it is the last slot', () => {
    const slot = {from: '10:00 AM', to: '12:00 PM', id: '1'};
    render(
      <TimeSlotRow
        slot={slot}
        slotIndex={0}
        totalSlots={1}
        onAddRow={mockOnAddRow}
        onRemoveRow={mockOnRemoveRow}
        canAdd={true}
        canRemove={true}
      />,
    );
    expect(screen.getByTestId('SquarePlusIcon')).toBeInTheDocument(); // Assuming SquarePlus renders with this testId
    expect(screen.queryByTestId('Trash2Icon')).not.toBeInTheDocument(); // Assuming Trash2 renders with this testId
  });

  test('renders Trash2 icon when canAdd is false or it is not the last slot', () => {
    const slot = {from: '10:00 AM', to: '12:00 PM', id: '1'};
    render(
      <TimeSlotRow
        slot={slot}
        slotIndex={0}
        totalSlots={2} // Not the last slot
        onAddRow={mockOnAddRow}
        onRemoveRow={mockOnRemoveRow}
        canAdd={true}
        canRemove={true}
      />,
    );
    expect(screen.queryByTestId('SquarePlusIcon')).not.toBeInTheDocument();
    expect(screen.getByTestId('Trash2Icon')).toBeInTheDocument();
  });

  test('calls onAddRow when SquarePlus icon is clicked', () => {
    const slot = {from: '10:00 AM', to: '12:00 PM', id: '1'};
    render(
      <TimeSlotRow
        slot={slot}
        slotIndex={0}
        totalSlots={1}
        onAddRow={mockOnAddRow}
        onRemoveRow={mockOnRemoveRow}
        canAdd={true}
        canRemove={true}
      />,
    );
    fireEvent.click(screen.getByTestId('SquarePlusIcon'));
    expect(mockOnAddRow).toHaveBeenCalledTimes(1);
  });

  test('calls onRemoveRow when Trash2 icon is clicked and canRemove is true', () => {
    const slot = {from: '10:00 AM', to: '12:00 PM', id: '1'};
    render(
      <TimeSlotRow
        slot={slot}
        slotIndex={0}
        totalSlots={2}
        onAddRow={mockOnAddRow}
        onRemoveRow={mockOnRemoveRow}
        canAdd={false}
        canRemove={true}
      />,
    );
    fireEvent.click(screen.getByTestId('Trash2Icon'));
    expect(mockOnRemoveRow).toHaveBeenCalledTimes(1);
  });

  test('does not call onRemoveRow when Trash2 icon is clicked and canRemove is false', () => {
    const slot = {from: '10:00 AM', to: '12:00 PM', id: '1'};
    render(
      <TimeSlotRow
        slot={slot}
        slotIndex={0}
        totalSlots={2}
        onAddRow={mockOnAddRow}
        onRemoveRow={mockOnRemoveRow}
        canAdd={false}
        canRemove={false}
      />,
    );
    fireEvent.click(screen.getByTestId('Trash2Icon'));
    expect(mockOnRemoveRow).not.toHaveBeenCalled();
  });
});
