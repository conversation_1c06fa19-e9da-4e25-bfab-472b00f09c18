import {render, screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import Inbox from '../Inbox';
import {InboxTabsEnum} from '@/enums/inbox-tabs.enum';
import {MockApiTabData} from '../mockData/mock-tab-data';
import {InboxTabContent} from '../components/InboxTabContent';

// Define types for mocked Tabs props
interface MockTabsProps {
  children: React.ReactNode;
  defaultValue: string;
  onValueChange: (value: string) => void;
}

// Mock child components
vi.mock('../components/InboxTopbar', () => ({
  InboxTopbar: vi.fn(() => <div data-testid="inbox-topbar" />),
}));

vi.mock('../components/InboxTabContent', () => ({
  InboxTabContent: vi.fn(() => <div data-testid="inbox-tab-content" />),
}));

// Mock the Tabs component from '@/components/ui/tab'
vi.mock('@/components/ui/tab', () => ({
  Tabs: ({children, defaultValue, onValueChange}: MockTabsProps) => (
    <div data-testid="mock-tabs" data-default-value={defaultValue}>
      <button onClick={() => onValueChange(InboxTabsEnum.ACTIVE)}>{InboxTabsEnum.ACTIVE}</button>
      <button onClick={() => onValueChange(InboxTabsEnum.ARCHIVED)}>{InboxTabsEnum.ARCHIVED}</button>
      {children}
    </div>
  ),
  TabsList: ({children}: {children: React.ReactNode}) => <div data-testid="mock-tabs-list">{children}</div>,
  TabsTrigger: ({value, children}: {value: string; children: React.ReactNode}) => (
    <button data-testid={`mock-tab-trigger-${value}`}>{children}</button>
  ),
  TabsContent: ({value, children}: {value: string; children: React.ReactNode}) => (
    <div data-testid={`mock-tab-content-${value}`}>{children}</div>
  ),
}));

describe('Inbox', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
  });

  test('renders the Chats heading', () => {
    render(<Inbox />);
    expect(screen.getByRole('heading', {name: /Chats/i})).toBeInTheDocument();
  });

  test('renders Tabs component with default value', () => {
    render(<Inbox />);
    const tabsComponent = screen.getByTestId('mock-tabs');
    expect(tabsComponent).toBeInTheDocument();
    expect(tabsComponent).toHaveAttribute('data-default-value', InboxTabsEnum.ACTIVE);
  });

  test('renders InboxTopbar and InboxTabContent components', () => {
    render(<Inbox />);
    expect(screen.getByTestId('inbox-topbar')).toBeInTheDocument();
    expect(screen.getByTestId('inbox-tab-content')).toBeInTheDocument();
  });

  test('InboxTabContent receives correct tabData for active tab initially', () => {
    render(<Inbox />);
    expect(InboxTabContent).toHaveBeenCalledWith(
      expect.objectContaining({
        tabData: MockApiTabData.find(tab => tab.tabName === InboxTabsEnum.ACTIVE),
      }),
      undefined,
    );
  });

  test('activeTab state changes on tab value change', () => {
    render(<Inbox />);

    // Initially, active tab is 'ACTIVE'
    expect(InboxTabContent).toHaveBeenCalledWith(
      expect.objectContaining({
        tabData: MockApiTabData.find(tab => tab.tabName === InboxTabsEnum.ACTIVE),
      }),
      undefined,
    );

    // Simulate clicking the "Archived Tab" button
    fireEvent.click(screen.getByText(InboxTabsEnum.ARCHIVED));

    // Now, active tab should be 'ARCHIVED'
    expect(InboxTabContent).toHaveBeenCalledWith(
      expect.objectContaining({
        tabData: MockApiTabData.find(tab => tab.tabName === InboxTabsEnum.ARCHIVED),
      }),
      undefined,
    );
  });

  // Negative test case: Ensure InboxTabContent handles empty tabData gracefully
  test('InboxTabContent receives empty object if activeTab does not match any tabData', () => {
    // Temporarily modify MockApiTabData to not include 'ACTIVE'
    const originalMockApiTabData = [...MockApiTabData];
    MockApiTabData.length = 0; // Clear the array
    // No need to push dummy data, just ensure it's empty or doesn't contain the active tab

    render(<Inbox />);

    expect(InboxTabContent).toHaveBeenCalledWith(
      expect.objectContaining({
        tabData: {}, // Expect an empty object
      }),
      undefined,
    );

    // Restore original MockApiTabData
    MockApiTabData.length = 0;
    originalMockApiTabData.forEach(item => MockApiTabData.push(item));
  });
});
