import {render, screen, fireEvent} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxConfirmTransfer} from '../components/InboxConfirmTransfer';
import {MESSAGES} from '@/constants/messages.constant';

// Mock necessary modules
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({open, onOpenChange, children}: {open: boolean; onOpenChange: () => void; children: React.ReactNode}) => (
    <div data-testid="mock-dialog" data-open={open}>
      <button onClick={onOpenChange} data-testid="dialog-overlay-close-button">
        Close Dialog
      </button>
      {children}
    </div>
  ),
  DialogContent: ({children, className}: {children: React.ReactNode; className: string}) => (
    <div data-testid="mock-dialog-content" className={className}>
      {children}
    </div>
  ),
  DialogTitle: ({children, className}: {children: React.ReactNode; className: string}) => (
    <h2 data-testid="mock-dialog-title" className={className}>
      {children}
    </h2>
  ),
  DialogOverlay: ({className}: {className: string}) => <div data-testid="mock-dialog-overlay" className={className} />,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({
    children,
    onClick,
    variant,
    className,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    variant?: string;
    className?: string;
  }) => (
    <button data-testid="mock-button" onClick={onClick} className={`${variant} ${className}`}>
      {children}
    </button>
  ),
}));

vi.mock('lucide-react', async () => {
  const actual = await vi.importActual('lucide-react');
  return {
    ...actual,
    X: () => <svg data-testid="mock-x-icon" />,
  };
});

describe('InboxConfirmTransfer', () => {
  const mockOnClose = vi.fn();
  const mockOnTransferSuccess = vi.fn();
  const mockDepartment = 'Sales';
  const mockAgent = 'John Doe';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders dialog with correct title and elements', () => {
    render(
      <InboxConfirmTransfer
        department={mockDepartment}
        agent={mockAgent}
        onClose={mockOnClose}
        onTransferSuccess={mockOnTransferSuccess}
      />,
    );

    expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('mock-dialog')).toHaveAttribute('data-open', 'true');
    expect(screen.getByTestId('mock-dialog-title')).toHaveTextContent(MESSAGES.CONFIRM_TRANSFER);
    expect(screen.getByPlaceholderText(MESSAGES.ENTER_OPTIONAL_REMARKS)).toBeInTheDocument();
    expect(screen.getByRole('button', {name: MESSAGES.CANCEL})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: MESSAGES.PROCEED})).toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', () => {
    render(
      <InboxConfirmTransfer
        department={mockDepartment}
        agent={mockAgent}
        onClose={mockOnClose}
        onTransferSuccess={mockOnTransferSuccess}
      />,
    );
    fireEvent.click(screen.getByLabelText('Close'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  test('calls onClose and onTransferSuccess when Proceed button is clicked', () => {
    render(
      <InboxConfirmTransfer
        department={mockDepartment}
        agent={mockAgent}
        onClose={mockOnClose}
        onTransferSuccess={mockOnTransferSuccess}
      />,
    );
    fireEvent.click(screen.getByRole('button', {name: MESSAGES.PROCEED}));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
    expect(mockOnTransferSuccess).toHaveBeenCalledTimes(1);
  });

  test('updates remark state on textarea change', () => {
    render(
      <InboxConfirmTransfer
        department={mockDepartment}
        agent={mockAgent}
        onClose={mockOnClose}
        onTransferSuccess={mockOnTransferSuccess}
      />,
    );
    const textarea = screen.getByPlaceholderText(MESSAGES.ENTER_OPTIONAL_REMARKS) as HTMLTextAreaElement;
    fireEvent.change(textarea, {target: {value: 'Test remark'}});
    expect(textarea.value).toBe('Test remark');
  });
});
