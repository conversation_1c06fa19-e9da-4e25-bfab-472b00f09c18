import {Button} from '@/components/ui';
import React from 'react';

export interface ButtonProps {
  label: string;
  onClick: () => void;
  className?: string;
  variant?: 'default' | 'link' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'primary' | null | undefined;
}
export interface InboxChatTransferContent {
  imageSrc: string;
  heading: string;
  subtitle: string;
  footerContent?: React.ReactNode;
}
interface InboxChatTransferCardProps extends InboxChatTransferContent {
  buttons: ButtonProps[];
}

const InboxChatTransferCard: React.FC<InboxChatTransferCardProps> = ({
  imageSrc,
  heading,
  subtitle,
  buttons,
  footerContent,
}) => {
  return (
    <div className="bg-gray-100 rounded-md p-4">
      <div className="flex flex-col items-center gap-2">
        <img src={imageSrc} alt="Banner" className="w-30 my-4 h-15" />
        <h2 className="text-base text-foreground font-medium ">{heading}</h2>
        <p className="text-sm text-foreground font-normal text-center">{subtitle}</p>
      </div>
      <div className="flex justify-center mt-4 space-x-4">
        {buttons.map(button => (
          <Button
            key={`${button.label}`}
            className={`font-light ${button.variant === 'outline' && 'text-foreground'} uppercase ${button.className ?? ''}`}
            variant={button.variant ?? 'default'}
            onClick={button.onClick}
          >
            {button.label}
          </Button>
        ))}
      </div>
      <div className="m-4 py-2">{footerContent}</div>
    </div>
  );
};

export default InboxChatTransferCard;
