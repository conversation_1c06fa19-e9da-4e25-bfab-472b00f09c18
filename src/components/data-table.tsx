'use client';
import type {ColumnDef} from '@tanstack/react-table';
import {flexRender, getCoreRowModel, useReactTable} from '@tanstack/react-table';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from '../components/ui/table';
import {Card, CardContent, CardHeader} from '../components/ui/card';
import {Button} from '../components/ui/button';
import {Input} from '../components/ui/input';
import PlusIcon from '../assets/icons/plus.svg';
import {useState} from 'react';
import {Filter, Search} from 'lucide-react';
import {cn} from '@/lib/utils';
import {FilterPanel} from './filters/FilterPanel';
import {type AppliedFilter, type FilterGroup} from '@/types';
import {useTranslation} from 'react-i18next';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchPlaceholder?: string;
  buttonLabel?: string;
  onSearch?: (value: string) => void;
  onButtonClick?: () => void;
  isLoading?: boolean;
  filterGroups?: FilterGroup[];
  handleApplyFilters?: (applliedFilters: AppliedFilter | null) => void;
  activeFilters?: AppliedFilter | null;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchPlaceholder = 'Search...',
  buttonLabel = 'Add New',
  onSearch,
  onButtonClick,
  filterGroups,
  handleApplyFilters,
  activeFilters,
}: Readonly<DataTableProps<TData, TValue>>) {
  const {t} = useTranslation();
  const [isFocused, setIsFocused] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    onSearch?.(value);
  };

  const handleOpenFilters = () => {
    setIsFilterOpen(true);
  };

  return (
    <>
      {filterGroups?.length && handleApplyFilters && (
        <FilterPanel
          isOpen={isFilterOpen}
          onClose={() => setIsFilterOpen(false)}
          groups={filterGroups ?? []}
          onApplyFilters={handleApplyFilters}
          activeFilters={activeFilters ?? null}
        />
      )}

      <Card className="border-0 shadow-xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="flex flex-1 h-10 items-center gap-2">
            <div className="relative max-w-[200px]">
              <div className={`absolute inset-y-0 left-3 flex items-center pointer-events-none`}>
                <Search className="h-4 w-4 text-[color:var(--color-icons)]" />
              </div>
              <div
                className={cn(
                  'absolute inset-0 pointer-events-none border rounded-md',
                  isFocused || searchValue ? 'border-primary' : 'border-input',
                )}
              >
                <span
                  data-testid="search-label"
                  className={cn(
                    'absolute px-1 bg-background text-xs transition-all duration-200',
                    isFocused || searchValue
                      ? '-top-2 left-3 text-primary'
                      : 'top-1/2 left-10 -translate-y-1/2 text-[color:var(--color-placeholders)]',
                  )}
                >
                  {searchPlaceholder}
                </span>
              </div>
              <Input
                data-testid="Search"
                className="max-w-full pl-10 pt-1 h-10 placeholder:opacity-0"
                value={searchValue}
                onChange={handleSearchChange}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
              />
            </div>
            {filterGroups?.length && (
              <Button
                data-testid="filter-button"
                variant="outline"
                onClick={handleOpenFilters}
                className={`w-11 h-10 border-input !hover:bg-[var(--primary-accent)/10] ${
                  activeFilters && Object.keys(activeFilters).length > 0 ? 'border-primary-accent' : ''
                }`}
                size={'sm'}
              >
                <div className="relative">
                  <Filter color="gray" />
                  {activeFilters && Object.keys(activeFilters).length > 0 && (
                    <span
                      data-testid="filter-indicator"
                      className="absolute top-0 right-0 block h-1 w-1 rounded-full ring-[var(--primary-accent)] ring-2 bg-[var(--primary-accent)]"
                    />
                  )}
                </div>
              </Button>
            )}
          </div>
          <Button
            data-testid="add-button"
            variant="primary"
            onClick={onButtonClick}
            className={cn('min-h-[40px] rounded-[5px] flex items-center gap-2 font-normal text-sm')}
          >
            <img src={PlusIcon} alt="Add" className="w-4 h-4" />
            {buttonLabel}
          </Button>
        </CardHeader>
        <CardContent className="border-0">
          <div className="rounded-[9px] overflow-hidden">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead
                        key={header.id}
                        className={cn(
                          'text-sm border-b border-[var(--table-border)]',
                          header.column.id === 'actions' || header.column.id === 'status' ? 'text-center' : 'text-left',
                        )}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row, index) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className={cn(index % 2 === 0 ? 'bg-white' : 'bg-[#F9F9F9]', 'rounded-[9px] border-0')}
                    >
                      {row.getVisibleCells().map(cell => (
                        <TableCell className="text-default text-xs" key={cell.id}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      {t('NO_RESULTS')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
