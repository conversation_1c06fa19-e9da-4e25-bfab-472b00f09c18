import React, {useEffect, useState} from 'react';
import {Dialog, DialogContent, DialogTitle, DialogOverlay} from '@/components/ui/dialog';

import {Button} from '@/components/ui/button';
import {MESSAGES} from '@/constants/messages.constant';
import {useTranslation} from 'react-i18next';
import {Check, ChevronRight, X} from 'lucide-react';
import {mockDepartments, mockDepartmentsAndAgents} from '../mockData/mock-department-agents';
import DepartmentAgentsInstruction from '../../../assets/inboxPanel/DepartmentAgentsInstruction.svg';
import {InboxConfirmTransfer} from '.';
import SuccessModal from '@/components/modals/SuccessModal';

interface InboxTransferChatDialogProps {
  isOpen: boolean;
  onClose: () => void;
  handleTransferCard?: () => void;
}

export const InboxTransferChatDialog: React.FC<InboxTransferChatDialogProps> = ({
  isOpen,
  onClose,
  handleTransferCard,
}) => {
  const {t} = useTranslation();
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [showConfirmTransfer, setShowConfirmTransfer] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const agentsForSelectedDepartment = selectedDepartment
    ? (mockDepartmentsAndAgents.find(dept => dept.departmentName === selectedDepartment)?.agents ?? [])
    : [];
  useEffect(() => {
    if (isOpen) {
      setSelectedDepartment(null);
      setSelectedAgent(null);
    }
  }, [isOpen]);

  const handleTransferSuccess = () => {
    setShowSuccessMessage(true);
    handleTransferCard?.();
  };
  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-40" />
        <DialogContent className="bg-white rounded-xl gap-0 shadow-lg w-[464px] p-0 overflow-hidden flex flex-col [&>button]:hidden z-50">
          {/* Header */}
          <div className="relative bg-white border-b border-gray-200 h-[60px] flex items-center">
            <DialogTitle className="pl-5 m-0 text-[14px] leading-[21px] font-medium text-[#313131] uppercase font-poppins">
              {t(MESSAGES.TRANSFER)}
            </DialogTitle>
            <button
              type="button"
              onClick={onClose}
              className="absolute right-5 top-1/2 -translate-y-1/2 text-[#4E4E4E] hover:text-gray-700"
              aria-label="Close"
            >
              <X size={20} />
            </button>
          </div>

          {/* Main Content */}
          <div className="flex flex-grow min-h-0 flex-col">
            {/* Header wrapper for both left and right labels */}
            <div className="h-[36px] flex border-t border-gray-200">
              <span className="text-sm font-normal text-[#D2D2D2] flex flex-[50%] items-center border-r border-gray-200 ml-4">
                {t(MESSAGES.SELECT_DEPARTMENT)}
              </span>
              <span className="text-sm font-normal text-[#D2D2D2] flex flex-[50%] items-center ml-4">
                {t(MESSAGES.CHOOSE_AGENT)}
              </span>
            </div>

            <div className="flex flex-grow min-h-0">
              {/* Left Panel */}
              <div className="w-1/2 border-r border-gray-200 flex flex-col h-full">
                <ul className="text-base font-normal h-[216px] overflow-y-auto">
                  {mockDepartments.map(item => (
                    <li
                      key={item}
                      className={`h-[36px] flex items-center justify-between cursor-pointer px-4 ${
                        item === selectedDepartment ? 'bg-gray-200' : 'hover:bg-gray-100'
                      }`}
                      onClick={() => setSelectedDepartment(item)}
                    >
                      <span className="font-poppins text-[#757575] text-[14px] leading-[13px] tracking-normal">
                        {item}
                      </span>
                      <ChevronRight size={18} className="text-[var(--text-gray-color)]" />
                    </li>
                  ))}
                </ul>
              </div>

              {/* Right Panel */}
              {selectedDepartment ? (
                <div className="w-1/2 flex flex-col h-full">
                  <ul className="text-base font-normal h-[216px] overflow-y-auto">
                    {agentsForSelectedDepartment.map(agent => (
                      <li
                        key={agent}
                        className={`h-[36px] flex items-center justify-between rounded cursor-pointer px-4 ${
                          agent === selectedAgent ? 'bg-gray-200' : 'hover:bg-gray-100'
                        }`}
                        onClick={() => setSelectedAgent(agent)}
                      >
                        <span className="font-poppins text-[#757575] text-[14px] leading-[13px] tracking-normal">
                          {agent}
                        </span>

                        {agent === selectedAgent && (
                          <div className="ml-auto mr-[2px]">
                            <Check className="text-[var(--powder-blue)]" />
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="w-1/2 flex flex-col items-center justify-center px-6 py-5 text-center text-muted-foreground h-full">
                  <img src={DepartmentAgentsInstruction} alt="No agents" className="w-[100px] h-[100px] mb-4" />
                  <p className="text-sm font-medium leading-4 text-black">
                    {t(MESSAGES.SELECT_DEPARTMENT_VIEW_AGENTS)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end h-[79px] border-t border-gray-200 px-6 gap-2">
            <Button
              variant="outline"
              className="border-[var(--border-gray-color)] text-[var(--text-gray-color)] font-normal"
              onClick={onClose}
            >
              {t(MESSAGES.CANCEL)}
            </Button>
            <Button
              className="text-white font-normal bg-[#496FDB] hover:bg-[#3f61c7] disabled:opacity-50"
              disabled={!selectedDepartment || !selectedAgent}
              onClick={() => {
                onClose(); // Close the current dialog
                setShowConfirmTransfer(true); // Open the confirmation dialog
              }}
            >
              {t(MESSAGES.TRANSFER)}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      {showConfirmTransfer && (
        <InboxConfirmTransfer
          department={selectedDepartment}
          agent={selectedAgent}
          onClose={() => setShowConfirmTransfer(false)}
          onTransferSuccess={handleTransferSuccess}
        />
      )}
      {showSuccessMessage && (
        <SuccessModal
          isOpen={showSuccessMessage}
          onClose={() => setShowSuccessMessage(false)}
          message={t(MESSAGES.TRANSFER_REQUEST_SUCCESSFUL)}
        />
      )}
    </>
  );
};
