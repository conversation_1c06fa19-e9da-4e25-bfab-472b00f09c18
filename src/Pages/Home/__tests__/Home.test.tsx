import {render, screen} from '@testing-library/react';
import {describe, it, expect, vi} from 'vitest';
import Home from '../Home';

// Mock the Button component
vi.mock('../../../components/ui/button', () => ({
  Button: ({children}: {children: React.ReactNode}) => <button data-testid="mock-button">{children}</button>,
}));

describe('Home', () => {
  it('renders the home page with button', () => {
    render(<Home />);

    // Check if the button is rendered
    const button = screen.getByTestId('mock-button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Home Page');
  });

  it('has the correct layout classes', () => {
    render(<Home />);

    // Check if the container has the correct classes
    const container = screen.getByTestId('mock-button').parentElement;
    expect(container).toHaveClass('flex');
    expect(container).toHaveClass('flex-col');
    expect(container).toHaveClass('items-center');
    expect(container).toHaveClass('justify-center');
    expect(container).toHaveClass('min-h-svh');
  });
});
