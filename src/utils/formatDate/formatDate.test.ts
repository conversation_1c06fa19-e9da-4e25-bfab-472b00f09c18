import {describe, it, expect} from 'vitest';
import {formatDate} from './formatDate';

describe('formatDate', () => {
  it('should format ISO date string correctly', () => {
    const input = '2023-05-20T00:00:00Z';
    const result = formatDate(input);

    // Format depends on system locale; for consistency, use regex or check common patterns
    expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/); // e.g., 5/20/2023 or 20/5/2023
  });

  it('should return "Invalid Date" for invalid input', () => {
    const result = formatDate('not-a-date');
    expect(result).toBe('Invalid Date');
  });
});
