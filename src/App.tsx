// src/App.tsx
import {BrowserRouter} from 'react-router-dom';
import AppRoutes from './routes/Routes';
import {getRouteConfig} from './routes/layoutRouteConfig';
import {Provider} from 'react-redux';
import {store} from './redux/store';
import {ThemeProvider} from './theme/ThemeProvider';
import {SnackbarProvider} from 'notistack';
import {Toaster} from './components';

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <SnackbarProvider Components={{custom: Toaster}} autoHideDuration={300000000}>
          <BrowserRouter>
            <AppRoutes routesConfig={getRouteConfig()} />
          </BrowserRouter>
        </SnackbarProvider>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
