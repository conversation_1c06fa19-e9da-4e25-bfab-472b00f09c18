import {render, screen} from '@testing-library/react';
import RoutingRules from '@/Pages/Settings/ChildrenPages/RoutingRules';
import {describe, it, expect, vi} from 'vitest';

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));
vi.mock('@/components/BreadCrumbs/CommonBreadCrumbs', () => ({
  CommonBreadcrumb: (props: any) => <nav data-testid="breadcrumb">{JSON.stringify(props.items)}</nav>,
}));
vi.mock('@/components/ui/card', () => ({
  Card: (props: any) => <div data-testid="card">{props.children}</div>,
}));
vi.mock('@/components/ui/radio-group', () => ({
  RadioGroup: (props: any) => <div data-testid="radio-group">{props.children}</div>,
  RadioGroupItem: (props: any) => <input type="radio" {...props} />,
}));
vi.mock('@/components/ui/label', () => ({
  Label: (props: any) => <label {...props}>{props.children}</label>,
}));
vi.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input {...props} />,
}));
vi.mock('lucide-react', () => ({
  Info: () => <svg data-testid="info-icon" />,
}));

describe('RoutingRules', () => {
  it('renders breadcrumb with correct items', () => {
    render(<RoutingRules />);
    const breadcrumb = screen.getByTestId('breadcrumb');
    expect(breadcrumb.textContent).toContain('SIDEBAR_MENU_SETTINGS');
    expect(breadcrumb.textContent).toContain('ROUTING_RULES');
  });

  it('renders the heading and description', () => {
    render(<RoutingRules />);
    expect(screen.getByText('SET_UP_ROUTING_RULES')).toBeInTheDocument();
    expect(screen.getByText('SET_UP_ROUTING_RULES_DESCRIPTION')).toBeInTheDocument();
  });

  it('renders both routing rule cards', () => {
    render(<RoutingRules />);
    const cards = screen.getAllByTestId('card');
    expect(cards.length).toBeGreaterThanOrEqual(2);
    expect(screen.getByLabelText('SIMULTANEOUS_ROUTING')).toBeInTheDocument();
    expect(screen.getByLabelText('DEPARTMENT_BASED_ROUTING')).toBeInTheDocument();
  });

  it('renders simultaneous routing card details', () => {
    render(<RoutingRules />);
    expect(screen.getByText('SIMULTANEOUS_ROUTING_DESCRIPTION')).toBeInTheDocument();
    expect(screen.getByText('REQUEST_TIMEOUT')).toBeInTheDocument();
    expect(screen.getByTestId('info-icon')).toBeInTheDocument();
    expect(screen.getByText('SECONDS')).toBeInTheDocument();
    const timeoutInput = screen.getByRole('spinbutton', {name: ''});
    expect(timeoutInput).toHaveAttribute('type', 'number');
    expect(timeoutInput).toHaveAttribute('min', '10');
    expect(timeoutInput).toHaveAttribute('max', '300');
    expect(timeoutInput).toHaveAttribute('value', '60');
  });

  it('renders department-based routing card', () => {
    render(<RoutingRules />);
    expect(screen.getByLabelText('DEPARTMENT_BASED_ROUTING')).toBeInTheDocument();
  });

  it('renders radio group with default value', () => {
    render(<RoutingRules />);
    const radioGroup = screen.getByTestId('radio-group');
    expect(radioGroup).toBeInTheDocument();
    // Simultaneous radio should be checked by default
    const radios = screen.getAllByRole('radio');
    expect(radios[0]).toHaveAttribute('value', 'simultaneous');
    expect(radios[1]).toHaveAttribute('value', 'department');
  });
});
