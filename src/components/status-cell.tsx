import {useActivateAgentStatusMutation, useDeactivateAgentStatusMutation} from '@/redux/agents/agentSlice';
import StatusSwitch from '@/components/status-switch';
import {AgentStatus, type AgentsDetails} from '@/types';
import React from 'react';

function StatusCell({agent}: {agent: AgentsDetails}) {
  const [activateAgentStatus] = useActivateAgentStatusMutation();
  const [deactivateAgentStatus] = useDeactivateAgentStatusMutation();

  const [localStatus, setLocalStatus] = React.useState(agent.status === AgentStatus.ACTIVE);

  const handleStatusChange = async (checked: boolean) => {
    try {
      if (checked) {
        await activateAgentStatus({id: agent.id, status: true}).unwrap();
      } else {
        await deactivateAgentStatus({id: agent.id, status: false}).unwrap();
      }
      setLocalStatus(checked);
    } catch (err) {
      console.error('Failed to update status:', err);
    }
  };

  return (
    <div className="flex justify-center">
      <StatusSwitch
        isActive={localStatus}
        // isLoading={isActivating || isDeactivating}
        onStatusChange={() => handleStatusChange}
      />
    </div>
  );
}

export default StatusCell;
