import React from 'react';
import type {Message} from '../types';
import ChatFilePreview from './ChatFilePreview';

interface InboxMessagesProps {
  messages: Message[];
}

export const InboxMessages: React.FC<InboxMessagesProps> = ({messages}) => {
  return (
    <>
      {messages.map(msg => (
        <div
          key={msg.id}
          className={`w-full flex flex-col ${msg.sender === 'right' ? 'items-end' : 'items-start'} mb-2`}
        >
          <div
            className={`relative text-center ${
              msg.sender === 'right'
                ? 'text-[var(--chat-text-dark)] bg-[var(--chat-bg-accent)] rounded-[20px_20px_0_20px] right-message-tail'
                : 'text-card-foreground bg-[var(--chat-bg-light)] rounded-[20px_20px_20px_0] left-message-tail'
            } max-w-[70%] px-3 py-2 text-sm flex flex-col items-start justify-start`}
          >
            <div data-testid="chat-message">{msg.text}</div>
            {msg.file && <ChatFilePreview file={msg.file} fileType={msg.fileType} fileName={msg.fileName} />}
          </div>
          <div
            className={`text-xs mt-1 text-gray-500 max-w-[70%] ${msg.sender === 'right' ? 'text-right' : 'text-left'}`}
          >
            {msg.time}
          </div>
        </div>
      ))}
    </>
  );
};
