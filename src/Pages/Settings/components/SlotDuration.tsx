import {useState} from 'react';
import {TimeSlotRow} from './TimeSlotRow';
import {WeekdayTimeSlot} from './WeekdayTimeSlot';

export const SlotDuration = ({showWeekdays = false}: {showWeekdays?: boolean}) => {
  const defaultTimeValues = [
    {from: '10:00 AM', to: '12:00 PM', id: '1'},
    {from: '01:00 PM', to: '03:00 PM', id: '2'},
    {from: '04:00 PM', to: '05:30 PM', id: '3'},
  ];

  const [dailyTimeRanges, setDailyTimeRanges] = useState(
    ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => ({
      day,
      timeSlots: [{from: '10:00 AM', to: '06:00 PM', id: '1'}],
    })),
  );

  const [timeRanges, setTimeRanges] = useState([defaultTimeValues[0]]); // State to manage the time ranges for non-weekday mode

  const handleAddRow = (dayIndex?: number) => {
    if (showWeekdays && dayIndex !== undefined) {
      setDailyTimeRanges(prevDailyRanges =>
        prevDailyRanges.map((dayData, i) => {
          if (i === dayIndex && dayData.timeSlots.length < 3) {
            return {
              ...dayData,
              timeSlots: [
                ...dayData.timeSlots,
                {from: '00:00 AM', to: '00:00 PM', id: (dayData.timeSlots.length + 1).toString()},
              ],
            };
          }
          return dayData;
        }),
      );
    } else if (!showWeekdays && timeRanges.length < 3) {
      const nextDefaultIndex = timeRanges.length;
      const newTimeRange = defaultTimeValues[nextDefaultIndex] || {from: '00:00 AM', to: '00:00 PM'};
      setTimeRanges(prevRanges => [...prevRanges, newTimeRange]);
    }
  };

  const handleRemoveRow = (indexToRemove: number, dayIndex?: number) => {
    if (showWeekdays && dayIndex !== undefined) {
      setDailyTimeRanges(prevDailyRanges =>
        prevDailyRanges.map((dayData, i) => {
          if (i === dayIndex && dayData.timeSlots.length > 1) {
            return {
              ...dayData,
              timeSlots: dayData.timeSlots.filter((_, slotIndex) => slotIndex !== indexToRemove),
            };
          }
          return dayData;
        }),
      );
    } else if (!showWeekdays && timeRanges.length > 1) {
      setTimeRanges(prevRanges => prevRanges.filter((_, i) => i !== indexToRemove));
    }
  };

  return (
    <div
      className={`space-y-4 px-3 py-2 border border-gray-200 rounded-2xl ${showWeekdays ? 'w-[700px]' : 'w-124'} flex flex-col justify-center ${
        /* Conditional width added: w-[700px] for weekdays, w-124 otherwise */
        showWeekdays ? 'h-auto' : timeRanges.length === 1 ? 'h-18.5' : timeRanges.length === 2 ? 'h-30' : 'h-43.5'
      }`}
    >
      {showWeekdays
        ? dailyTimeRanges.map((dayData, dayIndex) => (
            <WeekdayTimeSlot
              key={dayData.day}
              dayData={dayData}
              dayIndex={dayIndex}
              handleAddRow={handleAddRow}
              handleRemoveRow={handleRemoveRow}
            />
          ))
        : timeRanges.map((timeRange, i) => (
            <TimeSlotRow
              key={timeRange.id}
              slot={timeRange}
              slotIndex={i}
              totalSlots={timeRanges.length}
              onAddRow={() => handleAddRow()}
              onRemoveRow={() => handleRemoveRow(i)}
              canAdd={timeRanges.length < 3}
              canRemove={timeRanges.length > 1}
            />
          ))}
    </div>
  );
};
