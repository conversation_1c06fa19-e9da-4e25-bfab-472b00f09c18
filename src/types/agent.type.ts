import type {PaginatedResponse} from './api.type';

export enum AgentAvailability {
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy',
  OFFLINE = 'offline',
}

export type AgentsDetails = {
  id: string;
  name: string;
  email: string;
  mobile: string;
  departmentId: string;
  createdAt: string;
  availability: AgentAvailability;
  status: AgentStatus;
};

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export type AgentListResponse = PaginatedResponse<AgentsDetails>;
