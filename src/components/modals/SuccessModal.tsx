import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle} from '../ui/dialog';
import GreenTickIcon from '@/assets/icons/green-tick.svg';
import {Button} from '../ui';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
}

const SuccessModal: React.FC<SuccessModalProps> = ({isOpen, onClose, message}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] p-0 overflow-hidden [&>button]:hidden">
        <DialogHeader className="pt-6 px-6 flex flex-col items-center text-center">
          <div className="mb-4">
            <img src={GreenTickIcon} alt="Success" width={48} height={48} />
          </div>
          <DialogTitle className="text-[24px] font-semibold text-[#313131]">Success</DialogTitle>
        </DialogHeader>

        <div className="px-6 py-4 flex justify-center">
          <div className="w-[292px] h-[73px] flex items-center justify-center">
            <p className="text-[16px] text-[#8D919D] text-center">{message}</p>
          </div>
        </div>

        <DialogFooter className="w-full px-6 pb-6 flex justify-center">
          <Button
            variant="primary"
            onClick={onClose}
            className="h-[40px] w-[104px] flex items-center justify-center font-normal"
          >
            Done
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessModal;
