import React from 'react';
import {Circle} from 'lucide-react';
import {cn} from '@/lib/utils';
import {useTranslation} from 'react-i18next';

type AvailabilityStatusType = 'online' | 'offline' | 'busy' | 'away';

interface AvailabilityStatusProps {
  status: AvailabilityStatusType;
  className?: string;
}

const AvailabilityStatus: React.FC<AvailabilityStatusProps> = ({status, className}) => {
  const {t} = useTranslation();
  const statusConfig: Record<
    AvailabilityStatusType,
    {
      color: string;
      bgColor: string;
      label: string;
    }
  > = {
    online: {
      color: 'text-[var(--color-status-online)]',
      bgColor: 'bg-[var(--color-status-online)]',
      label: 'ONLINE',
    },
    offline: {
      color: 'text-[var(--color-status-offline)]',
      bgColor: 'bg-[var(--color-status-offline)]',
      label: 'OFFLINE',
    },
    busy: {
      color: 'text-[var(--color-status-busy)]',
      bgColor: 'bg-[var(--color-status-busy)]',
      label: 'BUSY',
    },
    away: {
      color: 'text-[var(--color-status-away)]',
      bgColor: 'bg-[var(--color-status-away)]',
      label: 'AWAY',
    },
  };

  const config = statusConfig[status?.toLowerCase() as AvailabilityStatusType] || statusConfig.offline;

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className="relative">
        <Circle className={cn('w-2 h-2 fill-current', config.color)} strokeWidth={0} />
      </div>
      <span className={cn('text-sm font-medium', config.color)}>{t(config.label)}</span>
    </div>
  );
};

export default AvailabilityStatus;
