import {describe, expect, it} from 'vitest';
import {hasPermission} from './permissionCheck';
import {PermissionsEnum} from '../../enums';

describe('hasPermission', () => {
  it('returns true when requiredPermission is undefined', () => {
    expect(hasPermission([PermissionsEnum.ADMIN], undefined)).toBe(true);
  });

  it('returns true when requiredPermission is an empty array', () => {
    expect(hasPermission([PermissionsEnum.ADMIN], [])).toBe(true);
  });

  it('returns true when user has at least one required permission', () => {
    expect(hasPermission([PermissionsEnum.ADMIN], [PermissionsEnum.ADMIN])).toBe(true);
  });

  it('returns false when user lacks all required permissions', () => {
    expect(hasPermission([PermissionsEnum.ADMIN], [PermissionsEnum.AGENT])).toBe(false);
  });

  it('returns true when user has multiple required permissions', () => {
    expect(hasPermission([PermissionsEnum.ADMIN, PermissionsEnum.AGENT], [PermissionsEnum.AGENT])).toBe(true);
  });

  it('returns false when none of the required permissions match', () => {
    expect(hasPermission([PermissionsEnum.ADMIN], [PermissionsEnum.AGENT, PermissionsEnum.BOT])).toBe(false);
  });

  it('returns true when user has at least one of multiple required permissions', () => {
    expect(hasPermission([PermissionsEnum.ADMIN], [PermissionsEnum.ADMIN, PermissionsEnum.AGENT])).toBe(true);
  });
});
