import {MESSAGES} from '@/constants/messages.constant';
import React, {Suspense} from 'react';
import {type RouteObject, useRoutes} from 'react-router-dom';
import {useTranslation} from 'react-i18next';

interface RoutesProps {
  routesConfig: RouteObject[];
}

/**
 * Renders the routes of the application based on the provided routes configuration.
 *
 * @param {RoutesProps} routesConfig - The configuration of routes to render.
 * @returns The rendered Routes component.
 */

const Routes: React.FC<RoutesProps> = ({routesConfig}) => {
  const {t} = useTranslation();
  const routes = useRoutes(routesConfig);
  return <Suspense fallback={t(MESSAGES.GLOBAL_LOADING_FALLBACK)}>{routes}</Suspense>;
};

export default Routes;
