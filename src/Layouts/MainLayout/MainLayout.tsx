import {Suspense, useEffect} from 'react';
import {Route, Routes, useLocation, useNavigate} from 'react-router-dom';
import NotFound from '../../Pages/NotFound/NotFound';
import mainLayoutRouteConfig from './mainLayoutRouteConfig';
import {SidebarProvider} from '../../components/ui/sidebar';
import {AppSidebar} from '../../components/AppSidebar/AppSidebar';
import {SideBarToggleBtn, TopBar} from '../../components';
import {MESSAGES} from '@/constants/messages.constant';
import {useTranslation} from 'react-i18next';
import {useAuth} from '@/hooks';
import {extractTokenFromUrl, removeTokenFromUrl} from '@/utils/tokenHelper';
import type {AuthResData} from '@/redux/auth/authSlice';

const MainLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const {login} = useAuth();
  const {t} = useTranslation();

  useEffect(() => {
    // Extract token from URL if present
    const token = extractTokenFromUrl(location);

    if (token) {
      // Login user with the token (assuming it's an access token)
      // In a real app, you might need to make an API call to validate the token
      // and get additional auth data like refresh_token and expires_in
      login({
        access_token: token,
        refresh_token: '', // This should come from your auth server
        expires_in: 3600, // Default to 1 hour, should come from auth server
        token_type: 'Bearer',
        'not-before-policy': 0,
        session_state: '',
        scope: '',
        refresh_expires_in: 86400, // Default to 24 hours
      } as AuthResData);

      // Remove token from URL
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      removeTokenFromUrl(location, navigate);
    }
  }, [login, location, navigate]);

  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width-icon': '4.5rem',
          '--sidebar-width': '14rem',
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <div className="flex-1 flex flex-col">
        <TopBar />
        <SideBarToggleBtn />
        <main className="flex-1 p-6 flex">
          <Suspense fallback={t(MESSAGES.GLOBAL_LOADING_FALLBACK)}>
            <Routes>
              {mainLayoutRouteConfig.map(({path, component: Component}) => (
                <Route key={path} path={path} element={<Component />} />
              ))}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
