import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest';
import {screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AgentsPage from '../AgentsPage';
import * as reactRouter from 'react-router-dom';
import * as reactRedux from '@/redux/agents/agentSlice';
import {renderWithStore} from '@/testUtils/test-utils';

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('@/redux/agents/agentSlice', () => ({
  ...vi.importActual('@/redux/agents/agentSlice'),
  useGetAgentsQuery: vi.fn(), // 👈 mock it here
  useActivateAgentStatusMutation: vi.fn(() => [vi.fn(), {isLoading: false}]),
  useDeactivateAgentStatusMutation: vi.fn(() => [vi.fn(), {isLoading: false}]),
}));

describe('AgentsPage', () => {
  const mockNavigate = vi.fn();
  const mockRefetch = vi.fn();
  const mockAgentsData = {data: [{id: 1, name: 'Agent Smith'}]};

  beforeEach(() => {
    vi.clearAllMocks();
    (reactRouter.useNavigate as any).mockReturnValue(mockNavigate);
    (reactRedux.useGetAgentsQuery as any).mockReturnValue({
      data: mockAgentsData,
      refetch: mockRefetch,
    });
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders DataTable with agents data', () => {
    renderWithStore(<AgentsPage />);
    expect(screen.getByText('ADD_AGENT')).toBeInTheDocument();
    expect(screen.getByTestId('Search')).toBeInTheDocument();
  });

  it('calls navigate when Add Agent button is clicked', async () => {
    const user = userEvent.setup();
    renderWithStore(<AgentsPage />);

    await user.click(screen.getByText('ADD_AGENT'));
    expect(mockNavigate).toHaveBeenCalledWith('/addAgent');
  });

  it('calls handleSearch when searching', async () => {
    const user = userEvent.setup();
    renderWithStore(<AgentsPage />);

    const searchInput = screen.getByTestId('Search');
    await user.type(searchInput, 'test search');
    expect(searchInput).toHaveValue('test search');
  });

  it('displays agent data from the API', () => {
    renderWithStore(<AgentsPage />);
    expect(screen.getByText('Agent Smith')).toBeInTheDocument();
  });
});
