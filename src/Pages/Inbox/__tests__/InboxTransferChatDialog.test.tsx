import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxTransferChatDialog} from '../components/InboxTransferChatDialog';
import {MESSAGES} from '@/constants/messages.constant';
import {mockDepartments, mockDepartmentsAndAgents} from '../mockData/mock-department-agents';

// Mock necessary modules
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({open, onOpenChange, children}: {open: boolean; onOpenChange: () => void; children: React.ReactNode}) => (
    <div data-testid="mock-dialog" data-open={open}>
      <button onClick={onOpenChange} data-testid="dialog-overlay-close-button">
        Close Dialog
      </button>
      {children}
    </div>
  ),
  DialogContent: ({children, className}: {children: React.ReactNode; className: string}) => (
    <div data-testid="mock-dialog-content" className={className}>
      {children}
    </div>
  ),
  DialogTitle: ({children, className}: {children: React.ReactNode; className: string}) => (
    <h2 data-testid="mock-dialog-title" className={className}>
      {children}
    </h2>
  ),
  DialogOverlay: ({className}: {className: string}) => <div data-testid="mock-dialog-overlay" className={className} />,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({
    children,
    onClick,
    disabled,
    variant,
    className,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    variant?: string;
    className?: string;
  }) => (
    <button
      data-testid="mock-button"
      onClick={onClick}
      disabled={disabled}
      className={`${variant} ${className}`}
      name={typeof children === 'string' ? children : undefined}
    >
      {children}
    </button>
  ),
}));

vi.mock('lucide-react', async () => {
  const actual = await vi.importActual('lucide-react');
  return {
    ...actual,
    Check: () => <svg data-testid="mock-check-icon" />,
    ChevronRight: () => <svg data-testid="mock-chevron-right-icon" />,
    X: () => <svg data-testid="mock-x-icon" />,
  };
});

vi.mock('../../../assets/inboxPanel/DepartmentAgentsInstruction.svg', () => ({
  default: 'department-agents-instruction.svg',
}));

vi.mock('../components/InboxConfirmTransfer', () => ({
  InboxConfirmTransfer: vi.fn(({onClose, onTransferSuccess, department, agent}) => (
    <div data-testid="mock-inbox-confirm-transfer">
      <button onClick={onClose} data-testid="confirm-transfer-close-button">
        Close Confirm Transfer
      </button>
      <button onClick={onTransferSuccess} data-testid="confirm-transfer-success-button">
        Confirm Transfer Success
      </button>
      <span data-testid="confirm-transfer-department">{department}</span>
      <span data-testid="confirm-transfer-agent">{agent}</span>
    </div>
  )),
}));

vi.mock('@/components/modals/SuccessModal', () => ({
  default: vi.fn(({isOpen, onClose, message}) => (
    <div data-testid="mock-success-modal" data-is-open={isOpen}>
      <button onClick={onClose} data-testid="success-modal-close-button">
        Close Success Modal
      </button>
      <span data-testid="success-modal-message">{message}</span>
    </div>
  )),
}));

describe('InboxTransferChatDialog', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders dialog when isOpen is true', () => {
    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);
    expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('mock-dialog')).toHaveAttribute('data-open', 'true');
    expect(screen.getByRole('heading', {name: MESSAGES.TRANSFER})).toBeInTheDocument();
    expect(screen.getByText(MESSAGES.SELECT_DEPARTMENT)).toBeInTheDocument();
    expect(screen.getByText(MESSAGES.CHOOSE_AGENT)).toBeInTheDocument();
    expect(screen.getByRole('button', {name: MESSAGES.CANCEL})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: MESSAGES.TRANSFER})).toBeInTheDocument();
  });

  test('does not render dialog when isOpen is false', () => {
    render(<InboxTransferChatDialog isOpen={false} onClose={mockOnClose} />);
    expect(screen.getByTestId('mock-dialog')).toHaveAttribute('data-open', 'false');
  });

  test('calls onClose when close button is clicked', () => {
    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);
    fireEvent.click(screen.getByLabelText('Close'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  test('selects a department and displays agents', () => {
    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);

    const firstDepartment = mockDepartments[0];
    fireEvent.click(screen.getByText(firstDepartment));
    expect(screen.getByText(firstDepartment).closest('li')).toHaveClass('bg-gray-200');

    const agentsOfFirstDepartment = mockDepartmentsAndAgents.find(
      dept => dept.departmentName === firstDepartment,
    )?.agents;
    agentsOfFirstDepartment?.forEach(agent => {
      expect(screen.getByText(agent)).toBeInTheDocument();
    });
  });

  test('selects an agent and shows check icon', () => {
    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);

    const firstDepartment = mockDepartments[0];
    fireEvent.click(screen.getByText(firstDepartment));

    const firstAgent = mockDepartmentsAndAgents.find(dept => dept.departmentName === firstDepartment)?.agents[0];
    if (firstAgent) {
      fireEvent.click(screen.getByText(firstAgent));
      expect(screen.getByText(firstAgent).closest('li')).toHaveClass('bg-gray-200');
      expect(screen.getByTestId('mock-check-icon')).toBeInTheDocument();
    }
  });

  test('transfer button is disabled initially and enabled after selecting department and agent', () => {
    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);
    const transferButton = screen.getByRole('button', {name: MESSAGES.TRANSFER});
    expect(transferButton).toBeDisabled();

    const firstDepartment = mockDepartments[0];
    fireEvent.click(screen.getByText(firstDepartment));
    expect(transferButton).toBeDisabled(); // Still disabled as agent not selected

    const firstAgent = mockDepartmentsAndAgents.find(dept => dept.departmentName === firstDepartment)?.agents[0];
    if (firstAgent) {
      fireEvent.click(screen.getByText(firstAgent));
      expect(transferButton).not.toBeDisabled();
    }
  });

  test('clicking transfer button opens InboxConfirmTransfer and closes current dialog', async () => {
    const {InboxConfirmTransfer} = await import('../components/InboxConfirmTransfer');
    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);

    const firstDepartment = mockDepartments[0];
    fireEvent.click(screen.getByText(firstDepartment));

    const firstAgent = mockDepartmentsAndAgents.find(dept => dept.departmentName === firstDepartment)?.agents[0];
    if (firstAgent) {
      fireEvent.click(screen.getByText(firstAgent));
    }

    fireEvent.click(screen.getByRole('button', {name: MESSAGES.TRANSFER}));

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalledTimes(1); // Current dialog closes
      expect(InboxConfirmTransfer).toHaveBeenCalledWith(
        expect.objectContaining({
          department: firstDepartment,
          agent: firstAgent,
        }),
        undefined,
      );
      expect(screen.getByTestId('mock-inbox-confirm-transfer')).toBeInTheDocument();
    });
  });

  test('onTransferSuccess from InboxConfirmTransfer opens SuccessModal', async () => {
    const SuccessModal = (await import('@/components/modals/SuccessModal')).default;

    render(<InboxTransferChatDialog isOpen={true} onClose={mockOnClose} />);

    const firstDepartment = mockDepartments[0];
    fireEvent.click(screen.getByText(firstDepartment));

    const firstAgent = mockDepartmentsAndAgents.find(dept => dept.departmentName === firstDepartment)?.agents[0];
    if (firstAgent) {
      fireEvent.click(screen.getByText(firstAgent));
    }

    fireEvent.click(screen.getByRole('button', {name: MESSAGES.TRANSFER}));

    await waitFor(() => {
      expect(screen.getByTestId('mock-inbox-confirm-transfer')).toBeInTheDocument();
    });

    // Simulate onTransferSuccess from InboxConfirmTransfer
    fireEvent.click(screen.getByTestId('confirm-transfer-success-button'));

    await waitFor(() => {
      expect(SuccessModal).toHaveBeenCalledWith(
        expect.objectContaining({
          isOpen: true,
          message: MESSAGES.TRANSFER_REQUEST_SUCCESSFUL,
          onClose: expect.any(Function),
        }),
        undefined,
      );
      expect(screen.getByTestId('mock-success-modal')).toBeInTheDocument();
      expect(screen.getByTestId('mock-success-modal')).toHaveAttribute('data-is-open', 'true');
      expect(screen.getByTestId('success-modal-message')).toHaveTextContent(MESSAGES.TRANSFER_REQUEST_SUCCESSFUL);
    });
  });
});
