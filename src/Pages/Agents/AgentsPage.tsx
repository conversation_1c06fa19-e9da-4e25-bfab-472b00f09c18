'use client';

import {agentColumns} from './AgentColumns';
import {DataTable} from '../../components/data-table';
import {useGetAgentsQuery} from '../../redux/agents/agentSlice';
import {useNavigate} from 'react-router-dom';
import {AgentAvailability, FilterItemType, type AppliedFilter, type FilterGroup} from '@/types';
import {useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useGetDepartmentsQuery} from '@/redux/departments/departmentSlice';
import {useDebounce} from '@/hooks';

const AgentsPage = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilter | null>(null);
  const {data: departments} = useGetDepartmentsQuery();
  const deptData = departments?.data ?? [];
  const [searchQuery, setSearchQuery] = useState<string>('');
  const filterGroups: FilterGroup[] = [
    {
      id: 'departments',
      name: 'Departments',
      items: deptData.map(department => ({
        id: department.id,
        label: department.name,
        type: FilterItemType.CHECKBOX,
      })),
    },
    {
      id: 'availability',
      name: 'Availability',
      items: [
        {id: AgentAvailability.ONLINE, label: t('ONLINE'), type: FilterItemType.CHIP},
        {id: AgentAvailability.AWAY, label: t('AWAY'), type: FilterItemType.CHIP},
        {id: AgentAvailability.BUSY, label: t('BUSY'), type: FilterItemType.CHIP},
        {id: AgentAvailability.OFFLINE, label: t('OFFLINE'), type: FilterItemType.CHIP},
      ],
    },
    {
      id: 'status',
      name: 'Status',
      items: [
        {id: 'active', label: t('ACTIVE'), type: FilterItemType.CHIP},
        {id: 'inactive', label: t('INACTIVE'), type: FilterItemType.CHIP},
      ],
    },
  ];
  const DEBOUNCE_DELAY = 300;

  const handleAddAgent = () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    navigate('/addAgent');
  };

  const debouncedQuery = useDebounce(searchQuery, DEBOUNCE_DELAY);

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const handleApplyFilters = (activeFilters: AppliedFilter | null) => {
    console.log('Active filters:', activeFilters);
    setAppliedFilters(activeFilters);
  };

  const queryParams = useMemo(() => {
    const params: Record<string, string | string[]> = {};
    if (debouncedQuery) params.search = debouncedQuery;

    if (appliedFilters) {
      for (const [key, values] of Object.entries(appliedFilters)) {
        if (values.length > 0) {
          params[key] = values;
        }
      }
    }
    return params;
  }, [debouncedQuery, appliedFilters]);

  const {data: agents} = useGetAgentsQuery(queryParams);

  return (
    <div className="w-full">
      <DataTable
        columns={agentColumns(t)}
        data={agents?.data ?? []}
        buttonLabel={t('ADD_AGENT')}
        searchPlaceholder={t('SEARCH')}
        onSearch={handleSearch}
        onButtonClick={handleAddAgent}
        filterGroups={filterGroups}
        handleApplyFilters={handleApplyFilters}
        activeFilters={appliedFilters}
      />
    </div>
  );
};

export default AgentsPage;
