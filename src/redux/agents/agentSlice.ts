import {apiSlice} from '@/redux/apiSlice';
import type {AgentListResponse, AgentsDetails} from '@/types';
import type {FilterOptions} from '@/types/filter.types';
import {ApiSliceIdentifier} from '@/constants';
import {buildFilter} from '@/utils/filter/filter';

const apiSliceIdentifier = ApiSliceIdentifier.AGENT_PORTAL_FACADE;

export const agentApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getAgents: builder.query<AgentListResponse, FilterOptions | void>({
      query: filterOptions => ({
        url: '/agents',
        apiSliceIdentifier,
        params: buildFilter(filterOptions),
      }),
    }),

    getAgentById: builder.query<AgentsDetails, {id: string | number; options?: FilterOptions}>({
      query: ({id, options}) => ({
        url: `/agents/${id}`,
        apiSliceIdentifier,
        params: JSON.stringify(options),
      }),
    }),

    createAgent: builder.mutation<AgentsDetails, Partial<AgentsDetails>>({
      query: agent => ({
        url: '/agents',
        method: 'POST',
        body: agent,
        apiSliceIdentifier,
      }),
    }),

    updateAgent: builder.mutation<AgentsDetails, {id: string | number; data: Partial<AgentsDetails>}>({
      query: ({id, data}) => ({
        url: `/agents/${id}`,
        method: 'PUT',
        body: data,
        apiSliceIdentifier,
      }),
    }),

    activateAgentStatus: builder.mutation<AgentsDetails, {id: string | number; status: boolean}>({
      query: ({id}) => ({
        url: `/agents/${id}/activate`,
        method: 'PUT',
        apiSliceIdentifier,
      }),
    }),

    deactivateAgentStatus: builder.mutation<AgentsDetails, {id: string | number; status: boolean}>({
      query: ({id}) => ({
        url: `/agents/${id}/deactivate`,
        method: 'PUT',
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useGetAgentsQuery,
  useGetAgentByIdQuery,
  useCreateAgentMutation,
  useUpdateAgentMutation,
  useActivateAgentStatusMutation,
  useDeactivateAgentStatusMutation,
} = agentApiSlice;
