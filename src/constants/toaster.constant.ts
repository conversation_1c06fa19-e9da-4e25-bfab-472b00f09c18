import type {ToasterStyleMap} from '@/types';

export const TOASTER_STYLES: ToasterStyleMap = {
  success: {
    bg: 'bg-success',
    text: 'text-success-foreground',
    border: 'border-1-solid-success',
    button: 'bg-success-foreground text-success',
  },
  error: {
    bg: 'bg-destructive',
    text: 'text-white',
    border: 'border-1-solid-destructive',
    button: 'bg-white text-destructive',
  },
  warning: {
    bg: 'bg-warning',
    text: 'text-warning-foreground',
    border: 'border-1-solid-warning',
    button: 'bg-black text-warning',
  },
  info: {
    bg: 'bg-info-background',
    text: 'text-info',
    border: 'border-1-solid-info',
    button: 'bg-info text-white',
  },
  destructive: {
    bg: 'bg-destructive',
    text: 'text-white',
    border: 'border-1-solid-destructive',
    button: 'bg-white text-destructive',
  },
};
