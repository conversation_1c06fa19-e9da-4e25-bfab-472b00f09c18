import {render, screen} from '@testing-library/react';
import {BrowserRouter} from 'react-router-dom';
import BusinessHours from '../ChildrenPages/BusinessHours';
import {describe, expect, test, vi} from 'vitest';

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
    },
  }),
}));

describe('BusinessHours Component', () => {
  test('renders Business Hours title', () => {
    render(
      <BrowserRouter>
        <BusinessHours />
      </BrowserRouter>,
    );
    expect(screen.getByText('SET_UP_BUSINESS_HOURS')).toBeInTheDocument();
  });

  // Add more tests here for other functionalities of BusinessHours.tsx
  // For example, testing tab changes, form submissions, etc.
});
