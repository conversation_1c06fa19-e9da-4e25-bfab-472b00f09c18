// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type Where = Record<any, any> | undefined;
// Define operators that match Sequelize operators
export enum FilterOperator {
  EQ = 'eq', // equals
  NE = 'ne', // not equals
  GT = 'gt', // greater than
  GTE = 'gte', // greater than or equal
  LT = 'lt', // less than
  LTE = 'lte', // less than or equal
  LIKE = '[Op.like]', // LIKE '%value%'
  NOT_LIKE = 'notLike', // NOT LIKE '%value%'
  IN = 'in', // IN [...]
  NOT_IN = 'notIn', // NOT IN [...]
  BETWEEN = 'between', // BETWEEN x AND y
  IS_NULL = 'isNull', // IS NULL
  IS_NOT_NULL = 'isNotNull', // IS NOT NULL
}

// Define a condition with an operator
export interface FilterCondition {
  [FilterOperator.EQ]?: Where;
  [FilterOperator.NE]?: Where;
  [FilterOperator.GT]?: Where;
  [FilterOperator.GTE]?: Where;
  [FilterOperator.LT]?: Where;
  [FilterOperator.LTE]?: Where;
  [FilterOperator.LIKE]?: string;
  [FilterOperator.NOT_LIKE]?: string;
  [FilterOperator.IN]?: Where[];
  [FilterOperator.NOT_IN]?: Where[];
  [FilterOperator.BETWEEN]?: [Where, Where];
  [FilterOperator.IS_NULL]?: boolean;
  [FilterOperator.IS_NOT_NULL]?: boolean;
}

// Define where clause structure
export interface WhereClause {
  [field: string]: FilterCondition | Where;
  AND?: WhereClause[];
  OR?: WhereClause[];
}

// Define sort direction
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

// Define sort item
export type SortItem = [string, SortDirection];

// Define include model
export interface IncludeModel {
  model: string;
  as?: string;
  attributes?: string[];
  where?: WhereClause;
  required?: boolean;
}

// Define complete filter options
export interface FilterOptions {
  where?: WhereClause;
  attributes?: string[];
  include?: IncludeModel[];
  order?: SortItem[];
  limit?: number;
  offset?: number;
  group?: string[];
}

export enum FilterItemType {
  CHECKBOX = 'checkbox',
  CHIP = 'chip',
}

export interface FilterItem {
  id: string;
  label: string;
  type: FilterItemType;
  selected?: boolean;
}

export interface FilterGroup {
  id: string;
  name: string;
  items: FilterItem[];
}

export type AppliedFilter = Record<string, string[]>;
