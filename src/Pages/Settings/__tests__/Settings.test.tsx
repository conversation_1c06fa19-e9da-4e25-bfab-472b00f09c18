import {render, screen, fireEvent} from '@testing-library/react';
import {BrowserRouter} from 'react-router-dom';
import Settings from '../Settings';
import {RouteConstant} from '@/constants/route.constant';
import {vi, test, expect, describe, beforeEach} from 'vitest';

// Mock the useNavigate hook
const mockedUseNavigate = vi.fn();
vi.mock('react-router-dom', async importOriginal => {
  const actual = await (importOriginal as any)();
  return {
    ...actual,
    useNavigate: () => mockedUseNavigate,
  };
});

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Return the key itself as the translated string
    i18n: {
      changeLanguage: vi.fn(),
      language: 'en',
    },
  }),
}));

describe('Settings Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    mockedUseNavigate.mockClear();
  });

  test('renders Settings text', () => {
    render(
      <BrowserRouter>
        <Settings />
      </BrowserRouter>,
    );
    expect(screen.getByText('SETTINGS')).toBeInTheDocument();
  });

  test('renders Business Hours link', () => {
    render(
      <BrowserRouter>
        <Settings />
      </BrowserRouter>,
    );
    expect(screen.getByText('SETTINGS_BUSINESS_HOURS_TITLE')).toBeInTheDocument();
  });

  test('navigates to Business Hours page on click', () => {
    render(
      <BrowserRouter>
        <Settings />
      </BrowserRouter>,
    );

    const businessHoursLink = screen.getByText('SETTINGS_BUSINESS_HOURS_TITLE');
    fireEvent.click(businessHoursLink);

    expect(mockedUseNavigate).toHaveBeenCalledTimes(1);
    expect(mockedUseNavigate).toHaveBeenCalledWith(RouteConstant.BUSINESS_HOURS);
  });
});
