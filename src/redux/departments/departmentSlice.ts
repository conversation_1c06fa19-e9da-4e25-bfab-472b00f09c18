import {apiSlice} from '../apiSlice';
import type {DepartmentListResponse, IDepartment} from '../../types';
import type {FilterOptions} from '../../types/filter.types';
import {buildFilter} from '../../utils/filter/filter';
import {ApiSliceIdentifier} from '@/constants';

const apiSliceIdentifier = ApiSliceIdentifier.AGENT_PORTAL_FACADE;

export const departmentApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDepartments: builder.query<DepartmentListResponse, FilterOptions | void>({
      query: filterOptions => ({
        url: '/departments',
        apiSliceIdentifier,
        params: buildFilter(filterOptions),
      }),
    }),

    getDepartmentById: builder.query<IDepartment, {id: string | number; options?: FilterOptions}>({
      query: ({id, options}) => ({
        url: `/departments/${id}`,
        apiSliceIdentifier,
        params: JSON.stringify(options),
      }),
    }),

    createDepartment: builder.mutation<IDepartment, {name: string}>({
      query: department => ({
        url: '/departments',
        method: 'POST',
        body: department,
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {useGetDepartmentsQuery, useGetDepartmentByIdQuery, useCreateDepartmentMutation} = departmentApiSlice;
