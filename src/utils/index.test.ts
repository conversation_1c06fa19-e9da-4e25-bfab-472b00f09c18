import {describe, it, expect, vi} from 'vitest';
import * as formatDateModule from './formatDate/formatDate';
import * as permissionCheckModule from './permissionCheck/permissionCheck';
import {formatDate, hasPermission} from './index';
import {PermissionsEnum} from '@/enums';

describe('utils/index.ts', () => {
  it('should correctly export formatDate function', () => {
    // Mock the original function
    const mockFormatDate = vi.spyOn(formatDateModule, 'formatDate');

    // Call the exported function
    formatDate('2023-01-01');

    // Verify the original function was called
    expect(mockFormatDate).toHaveBeenCalledWith('2023-01-01');
  });

  it('should correctly export hasPermission function', () => {
    // Mock the original function
    const mockHasPermission = vi.spyOn(permissionCheckModule, 'hasPermission');
    const userPermissions = [PermissionsEnum.ADMIN];
    const requiredPermissions = [PermissionsEnum.AGENT];

    // Call the exported function
    hasPermission(userPermissions, requiredPermissions);

    // Verify the original function was called
    expect(mockHasPermission).toHaveBeenCalledWith(userPermissions, requiredPermissions);
  });
});
