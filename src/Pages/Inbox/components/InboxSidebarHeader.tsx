import {MESSAGES} from '@/constants/messages.constant';
import {TabsList, TabsTrigger} from '@/components/ui/tabs';
import {useTranslation} from 'react-i18next';

export const InboxSidebarHeader = () => {
  const {t} = useTranslation();

  return (
    <TabsList className="grid grid-cols-2 w-full h-[46px] bg-transparent border-b border-border rounded-none p-0">
      <TabsTrigger value="self" className="tab-trigger-base font-sans p-0">
        {t(MESSAGES.INBOX_TAB_SELF)}
      </TabsTrigger>
      <TabsTrigger value="other-agents" className="tab-trigger-base font-sans p-0">
        {t(MESSAGES.INBOX_TAB_OTHER_AGENTS)}
      </TabsTrigger>
    </TabsList>
  );
};
