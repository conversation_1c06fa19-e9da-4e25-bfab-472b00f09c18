import {render, screen} from '@testing-library/react';
import {describe, test, expect} from 'vitest';
import {InboxMessages} from '../components/InboxMessages';
import type {Message} from '../types';

describe('InboxMessages', () => {
  const mockMessages: Message[] = [
    {id: 1, text: 'Hello!', sender: 'left', time: '10:00 AM'},
    {id: 2, text: 'Hi there!', sender: 'right', time: '10:05 AM'},
    {id: 3, text: 'How are you?', sender: 'left', time: '10:10 AM'},
  ];

  test('renders messages correctly with appropriate styling for sender', () => {
    render(<InboxMessages messages={mockMessages} />);

    const leftMessages = screen.getAllByText(/Hello!|How are you?/);
    expect(leftMessages.length).toBe(2);
    leftMessages.forEach(msg => {
      const parentDiv = msg.closest('.flex.flex-col');
      expect(parentDiv).toHaveClass('items-start');
      expect(msg.parentElement).toHaveClass(
        'bg-[var(--chat-bg-light)]',
        'rounded-[20px_20px_20px_0]',
        'left-message-tail',
      );
    });

    const rightMessages = screen.getAllByText(/Hi there!/);
    expect(rightMessages.length).toBe(1);
    rightMessages.forEach(msg => {
      const alignmentContainer = msg.parentElement?.parentElement;
      expect(alignmentContainer).toHaveClass('items-end');
      expect(msg.parentElement).toHaveClass(
        'bg-[var(--chat-bg-accent)]',
        'rounded-[20px_20px_0_20px]',
        'right-message-tail',
      );
    });
  });

  test('renders message text and time', () => {
    render(<InboxMessages messages={mockMessages} />);

    expect(screen.getByText('Hello!')).toBeInTheDocument();
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
    expect(screen.getByText('Hi there!')).toBeInTheDocument();
    expect(screen.getByText('10:05 AM')).toBeInTheDocument();
    expect(screen.getByText('How are you?')).toBeInTheDocument();
    expect(screen.getByText('10:10 AM')).toBeInTheDocument();
  });

  test('renders no messages when the messages array is empty', () => {
    render(<InboxMessages messages={[]} />);
    expect(screen.queryByTestId('chat-message')).not.toBeInTheDocument();
  });

  test('ensures full line coverage for InboxMessages component', () => {
    // This test implicitly covers all lines by rendering the component with various message types
    // and asserting on their presence and styling.
    render(<InboxMessages messages={mockMessages} />);

    // Check for the presence of all messages
    expect(screen.getByText('Hello!')).toBeInTheDocument();
    expect(screen.getByText('Hi there!')).toBeInTheDocument();
    expect(screen.getByText('How are you?')).toBeInTheDocument();

    // Check for the presence of all timestamps
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
    expect(screen.getByText('10:05 AM')).toBeInTheDocument();
    expect(screen.getByText('10:10 AM')).toBeInTheDocument();

    // Verify styling for left-aligned message
    const leftMessageText = screen.getByText('Hello!');
    const leftMessageContainer = leftMessageText.closest('.flex.flex-col');
    expect(leftMessageContainer).toHaveClass('items-start');
    expect(leftMessageText.parentElement).toHaveClass('bg-[var(--chat-bg-light)]');

    // Verify styling for right-aligned message
    const rightMessageText = screen.getByText('Hi there!');
    const rightMessageAlignmentContainer = rightMessageText.parentElement?.parentElement;
    expect(rightMessageAlignmentContainer).toHaveClass('items-end');
    expect(rightMessageText.parentElement).toHaveClass('bg-[var(--chat-bg-accent)]');
  });
});
