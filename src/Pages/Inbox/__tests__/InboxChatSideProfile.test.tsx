import {render, screen} from '@testing-library/react';
import {describe, test, expect, vi, beforeEach} from 'vitest';
import {InboxChatSideProfile} from '../components/InboxChatSideProfile';

// Mock lucide-react icons
vi.mock('lucide-react', async () => {
  const actual = await vi.importActual('lucide-react');
  return {
    ...actual,
    CircleUser: () => <svg data-testid="mock-circle-user-icon" />,
    MessagesSquare: () => <svg data-testid="mock-messages-square-icon" />,
  };
});

vi.mock('../../../assets/inboxPanel/UserInfo.svg', () => ({
  default: 'UserInfo.svg',
}));

describe('InboxChatSideProfile', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders all three lucide icons', () => {
    render(<InboxChatSideProfile />);
    expect(screen.getByTestId('mock-circle-user-icon')).toBeInTheDocument();
    expect(screen.getByAltText('User Info')).toBeInTheDocument();
    expect(screen.getByTestId('mock-messages-square-icon')).toBeInTheDocument();
  });

  test('renders the main container div with correct classes', () => {
    const {container} = render(<InboxChatSideProfile />);
    expect(container.firstChild).toHaveClass('w-12');
    expect(container.firstChild).toHaveClass('bg-white');
    expect(container.firstChild).toHaveClass('flex');
    expect(container.firstChild).toHaveClass('flex-col');
    expect(container.firstChild).toHaveClass('items-center');
    expect(container.firstChild).toHaveClass('pt-6');
    expect(container.firstChild).toHaveClass('border-l');
    expect(container.firstChild).toHaveClass('border-gray-300');
  });
});
