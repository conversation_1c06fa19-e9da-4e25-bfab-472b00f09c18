import type {ToasterIcon} from '@/types';
import {CheckCircle, XCircle, AlertTriangle, Info} from 'lucide-react';

export const TOASTER_ICONS: ToasterIcon = {
  success: () => <CheckCircle size={24} />,
  error: () => <XCircle color="var(--destructive-foreground)" size={24} />,
  warning: () => <AlertTriangle size={24} />,
  info: () => <Info color="var(--info)" size={24} />,
  destructive: () => <XCircle color="var(--destructive-foreground)" size={24} />,
};
